"use client";

import { ConstructionTeamForm } from "@/components/forms/construction-team-form";
import {
  Sheet,
  SheetContent,
  SheetDes<PERSON>,
  SheetHeader,
  SheetTitle,
} from "@midday/ui/sheet";

type Props = {
  projectId: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
};

export function ConstructionTeamSheet({ projectId, open, onOpenChange }: Props) {
  const handleSuccess = () => {
    onOpenChange(false);
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="w-full sm:max-w-lg">
        <SheetHeader>
          <SheetTitle>Create Construction Team</SheetTitle>
          <SheetDescription>
            Add a new construction team to manage BIM elements and collaborate 
            on this project. Teams can be assigned specific elements and camera views.
          </SheetDescription>
        </SheetHeader>
        
        <div className="mt-6">
          <ConstructionTeamForm
            projectId={projectId}
            onSuccess={handleSuccess}
            onCancel={handleCancel}
          />
        </div>
      </SheetContent>
    </Sheet>
  );
}