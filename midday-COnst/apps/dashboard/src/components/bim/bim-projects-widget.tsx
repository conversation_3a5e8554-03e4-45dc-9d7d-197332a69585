"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@midday/ui/card";
import { <PERSON><PERSON> } from "@midday/ui/button";
import { Badge } from "@midday/ui/badge";
import { Icons } from "@midday/ui/icons";
import { Progress } from "@midday/ui/progress";
import Link from "next/link";

// Mock data - in real implementation, this would come from tRPC query
const mockBIMProjects = [
  {
    id: "project_123",
    name: "Downtown Office Complex",
    projectType: "commercial" as const,
    projectProgress: 45,
    ifcRoute: "/storage/projects/project_123/model.ifc",
    hasModel: true,
    lastActivity: new Date("2024-03-15"),
    elementsCount: 1247,
    qtoCalculated: true,
    estimatedCost: 2500000,
    currency: "USD",
  },
  {
    id: "project_456",
    name: "Residential Tower A",
    projectType: "residential" as const,
    projectProgress: 78,
    ifcRoute: "/storage/projects/project_456/model.ifc",
    hasModel: true,
    lastActivity: new Date("2024-03-14"),
    elementsCount: 2341,
    qtoCalculated: false,
    estimatedCost: 1800000,
    currency: "USD",
  },
  {
    id: "project_789",
    name: "Bridge Renovation",
    projectType: "heavy_civil" as const,
    projectProgress: 23,
    ifcRoute: null,
    hasModel: false,
    lastActivity: new Date("2024-03-13"),
    elementsCount: 0,
    qtoCalculated: false,
    estimatedCost: 5200000,
    currency: "USD",
  },
];

export function BIMProjectsWidget() {
  const projectsWithModels = mockBIMProjects.filter(p => p.hasModel);
  const totalElements = mockBIMProjects.reduce((sum, p) => sum + p.elementsCount, 0);
  const projectsWithQTO = mockBIMProjects.filter(p => p.qtoCalculated).length;

  const getProjectTypeColor = (type: string) => {
    const colors = {
      residential: "bg-green-100 text-green-800",
      commercial: "bg-blue-100 text-blue-800",
      institutional: "bg-purple-100 text-purple-800",
      mixed_use: "bg-orange-100 text-orange-800",
      industrial: "bg-gray-100 text-gray-800",
      heavy_civil: "bg-red-100 text-red-800",
    };
    return colors[type as keyof typeof colors] || "bg-gray-100 text-gray-800";
  };

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Icons.Building className="w-5 h-5" />
            <span>BIM Projects</span>
          </CardTitle>
          <Link href="/tracker">
            <Button variant="outline" size="sm">
              View All
            </Button>
          </Link>
        </div>
      </CardHeader>
      <CardContent>
        {/* Summary Stats */}
        <div className="grid grid-cols-3 gap-4 mb-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {projectsWithModels.length}
            </div>
            <div className="text-sm text-gray-500">
              With 3D Models
            </div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {totalElements.toLocaleString()}
            </div>
            <div className="text-sm text-gray-500">
              Total Elements
            </div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">
              {projectsWithQTO}
            </div>
            <div className="text-sm text-gray-500">
              QTO Calculated
            </div>
          </div>
        </div>

        {/* Project List */}
        <div className="space-y-3">
          {mockBIMProjects.slice(0, 4).map((project) => (
            <div
              key={project.id}
              className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center space-x-3 flex-1">
                <div className="flex flex-col space-y-1">
                  <div className="flex items-center space-x-2">
                    {project.hasModel ? (
                      <Icons.Building className="w-4 h-4 text-blue-600" />
                    ) : (
                      <Icons.AlertCircle className="w-4 h-4 text-gray-400" />
                    )}
                    <Link
                      href={`/tracker/${project.id}`}
                      className="font-medium text-gray-900 hover:text-blue-600"
                    >
                      {project.name}
                    </Link>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Badge 
                      variant="outline" 
                      className={`text-xs ${getProjectTypeColor(project.projectType)}`}
                    >
                      {project.projectType.replace("_", " ")}
                    </Badge>
                    
                    {project.hasModel && (
                      <Badge variant="secondary" className="text-xs">
                        {project.elementsCount.toLocaleString()} elements
                      </Badge>
                    )}
                    
                    {project.qtoCalculated && (
                      <Badge variant="outline" className="text-xs text-green-600 border-green-200">
                        QTO Ready
                      </Badge>
                    )}
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <div className="text-right">
                  <div className="text-sm font-medium">
                    {formatCurrency(project.estimatedCost, project.currency)}
                  </div>
                  <div className="text-xs text-gray-500">
                    {project.projectProgress}% complete
                  </div>
                </div>
                
                <div className="w-16">
                  <Progress 
                    value={project.projectProgress} 
                    className="h-2"
                  />
                </div>

                <Link href={`/tracker/${project.id}`}>
                  <Button variant="ghost" size="sm">
                    <Icons.ArrowForward className="w-4 h-4" />
                  </Button>
                </Link>
              </div>
            </div>
          ))}
        </div>

        {/* Quick Actions */}
        <div className="flex items-center justify-between mt-6 pt-4 border-t">
          <div className="text-sm text-gray-500">
            {mockBIMProjects.length} total projects
          </div>
          
          <div className="flex space-x-2">
            <Link href="/tracker?filter=bim">
              <Button variant="outline" size="sm">
                <Icons.Filter className="w-4 h-4 mr-2" />
                BIM Projects
              </Button>
            </Link>
            
            <Link href="/tracker/new">
              <Button size="sm">
                <Icons.Add className="w-4 h-4 mr-2" />
                New Project
              </Button>
            </Link>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * Compact BIM Projects Widget for smaller dashboard spaces
 */
export function BIMProjectsCompactWidget() {
  const projectsWithModels = mockBIMProjects.filter(p => p.hasModel);
  
  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <CardTitle className="text-base flex items-center space-x-2">
          <Icons.Building className="w-4 h-4" />
          <span>BIM Overview</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Projects with 3D Models</span>
            <span className="font-medium">{projectsWithModels.length}</span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Total Elements</span>
            <span className="font-medium">
              {mockBIMProjects.reduce((sum, p) => sum + p.elementsCount, 0).toLocaleString()}
            </span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">QTO Calculated</span>
            <span className="font-medium">
              {mockBIMProjects.filter(p => p.qtoCalculated).length} / {mockBIMProjects.length}
            </span>
          </div>
        </div>

        <div className="mt-4 pt-3 border-t">
          <Link href="/tracker">
            <Button variant="outline" size="sm" className="w-full">
              View All Projects
            </Button>
          </Link>
        </div>
      </CardContent>
    </Card>
  );
}