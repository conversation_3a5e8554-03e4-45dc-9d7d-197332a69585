"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@midday/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@midday/ui/card";
import { Checkbox } from "@midday/ui/checkbox";
import { Icons } from "@midday/ui/icons";
import { Input } from "@midday/ui/input";
import { ScrollArea } from "@midday/ui/scroll-area";
import { Badge } from "@midday/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@midday/ui/select";

// Mock IFC elements for demonstration
// In a real implementation, these would come from parsing the IFC file
interface IFCElement {
  guid: string;
  type: string;
  name: string;
  level?: string;
  material?: string;
  isSelected: boolean;
}

const mockElements: IFCElement[] = [
  { guid: "2O2Fr$t4X7Zf8NOew3FLOH", type: "IfcColumn", name: "Column C1", level: "Level 1", material: "Concrete", isSelected: false },
  { guid: "2O2Fr$t4X7Zf8NOew3FLOi", type: "IfcBeam", name: "Beam B1", level: "Level 1", material: "Steel", isSelected: false },
  { guid: "2O2Fr$t4X7Zf8NOew3FLOj", type: "IfcWall", name: "Wall W1", level: "Level 1", material: "Concrete", isSelected: false },
  { guid: "2O2Fr$t4X7Zf8NOew3FLOk", type: "IfcSlab", name: "Slab S1", level: "Level 1", material: "Concrete", isSelected: false },
  { guid: "2O2Fr$t4X7Zf8NOew3FLOl", type: "IfcColumn", name: "Column C2", level: "Level 2", material: "Concrete", isSelected: false },
  { guid: "2O2Fr$t4X7Zf8NOew3FLOm", type: "IfcBeam", name: "Beam B2", level: "Level 2", material: "Steel", isSelected: false },
  { guid: "2O2Fr$t4X7Zf8NOew3FLOn", type: "IfcWall", name: "Wall W2", level: "Level 2", material: "Concrete", isSelected: false },
  { guid: "2O2Fr$t4X7Zf8NOew3FLOo", type: "IfcDoor", name: "Door D1", level: "Level 1", material: "Wood", isSelected: false },
  { guid: "2O2Fr$t4X7Zf8NOew3FLOp", type: "IfcWindow", name: "Window W1", level: "Level 1", material: "Glass", isSelected: false },
  { guid: "2O2Fr$t4X7Zf8NOew3FLOq", type: "IfcStair", name: "Stair ST1", level: "Level 1", material: "Concrete", isSelected: false },
];

type Props = {
  projectId: string;
  selectedGuids?: string[];
  onSelectionChange: (guids: string[]) => void;
  onHighlight?: (guids: string[]) => void;
  maxHeight?: string;
};

export function ElementSelector({
  projectId,
  selectedGuids = [],
  onSelectionChange,
  onHighlight,
  maxHeight = "400px",
}: Props) {
  const [elements, setElements] = useState<IFCElement[]>(
    mockElements.map(el => ({
      ...el,
      isSelected: selectedGuids.includes(el.guid),
    }))
  );
  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState("all");
  const [filterLevel, setFilterLevel] = useState("all");

  // Filter elements based on search and filters
  const filteredElements = elements.filter(element => {
    const matchesSearch = element.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         element.type.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = filterType === "all" || element.type === filterType;
    const matchesLevel = filterLevel === "all" || element.level === filterLevel;
    
    return matchesSearch && matchesType && matchesLevel;
  });

  // Get unique types and levels for filters
  const uniqueTypes = [...new Set(elements.map(el => el.type))];
  const uniqueLevels = [...new Set(elements.map(el => el.level).filter(Boolean))];

  const handleElementToggle = (guid: string) => {
    const updatedElements = elements.map(el =>
      el.guid === guid ? { ...el, isSelected: !el.isSelected } : el
    );
    setElements(updatedElements);

    const selectedIds = updatedElements
      .filter(el => el.isSelected)
      .map(el => el.guid);
    
    onSelectionChange(selectedIds);
  };

  const handleSelectAll = () => {
    const filteredGuids = filteredElements.map(el => el.guid);
    const allSelected = filteredGuids.every(guid => 
      selectedGuids.includes(guid)
    );

    const updatedElements = elements.map(el => ({
      ...el,
      isSelected: filteredGuids.includes(el.guid) ? !allSelected : el.isSelected,
    }));
    
    setElements(updatedElements);

    const selectedIds = updatedElements
      .filter(el => el.isSelected)
      .map(el => el.guid);
    
    onSelectionChange(selectedIds);
  };

  const handleClearSelection = () => {
    const updatedElements = elements.map(el => ({ ...el, isSelected: false }));
    setElements(updatedElements);
    onSelectionChange([]);
  };

  const handleHighlightSelected = () => {
    const selectedIds = elements
      .filter(el => el.isSelected)
      .map(el => el.guid);
    
    onHighlight?.(selectedIds);
  };

  const selectedCount = elements.filter(el => el.isSelected).length;
  const filteredSelected = filteredElements.filter(el => el.isSelected).length;

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-lg">BIM Elements</CardTitle>
            <p className="text-sm text-muted-foreground">
              {selectedCount} of {elements.length} elements selected
            </p>
          </div>
          {selectedCount > 0 && onHighlight && (
            <Button
              size="sm"
              variant="outline"
              onClick={handleHighlightSelected}
            >
              <Icons.Eye className="mr-2 h-4 w-4" />
              Highlight
            </Button>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Search and Filters */}
        <div className="space-y-3">
          <Input
            placeholder="Search elements by name or type..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full"
          />
          
          <div className="flex gap-2">
            <Select value={filterType} onValueChange={setFilterType}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Filter by type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                {uniqueTypes.map(type => (
                  <SelectItem key={type} value={type}>
                    {type.replace('Ifc', '')}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Select value={filterLevel} onValueChange={setFilterLevel}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Filter by level" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Levels</SelectItem>
                {uniqueLevels.map(level => (
                  <SelectItem key={level} value={level!}>
                    {level}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Selection Controls */}
        <div className="flex justify-between items-center">
          <div className="flex gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={handleSelectAll}
            >
              {filteredSelected === filteredElements.length ? 'Deselect' : 'Select'} All
            </Button>
            {selectedCount > 0 && (
              <Button
                size="sm"
                variant="outline"
                onClick={handleClearSelection}
              >
                Clear Selection
              </Button>
            )}
          </div>
          
          {filteredElements.length !== elements.length && (
            <Badge variant="secondary">
              {filteredElements.length} filtered
            </Badge>
          )}
        </div>

        {/* Elements List */}
        <ScrollArea style={{ height: maxHeight }}>
          <div className="space-y-2">
            {filteredElements.map((element) => (
              <div
                key={element.guid}
                className={`flex items-center space-x-3 p-3 rounded-lg border transition-colors ${
                  element.isSelected
                    ? 'bg-primary/5 border-primary/20'
                    : 'hover:bg-muted/50'
                }`}
              >
                <Checkbox
                  checked={element.isSelected}
                  onCheckedChange={() => handleElementToggle(element.guid)}
                />
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2">
                    <span className="font-medium text-sm truncate">
                      {element.name}
                    </span>
                    <Badge variant="outline" className="text-xs">
                      {element.type.replace('Ifc', '')}
                    </Badge>
                  </div>
                  
                  <div className="flex gap-2 mt-1">
                    {element.level && (
                      <span className="text-xs text-muted-foreground">
                        {element.level}
                      </span>
                    )}
                    {element.material && (
                      <span className="text-xs text-muted-foreground">
                        • {element.material}
                      </span>
                    )}
                  </div>
                  
                  <span className="text-xs text-muted-foreground font-mono">
                    {element.guid}
                  </span>
                </div>
                
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => onHighlight?.([element.guid])}
                  disabled={!onHighlight}
                >
                  <Icons.Eye className="h-4 w-4" />
                </Button>
              </div>
            ))}
            
            {filteredElements.length === 0 && (
              <div className="text-center py-8">
                <Icons.Search className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-muted-foreground">No elements found</p>
                <p className="text-sm text-muted-foreground">
                  Try adjusting your search or filters
                </p>
              </div>
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}