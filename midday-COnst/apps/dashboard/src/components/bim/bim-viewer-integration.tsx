"use client";

import { useState, useRef, useCallback } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Element<PERSON>ist,
  QTOCalculator,
  TeamAssignmentPanel,
  ViewerToolbar,
  MeasurementTool,
  useElementSelection,
  useQTO,
  BIMMiddayIntegration,
  MiddayQTOCalculator,
  MockMiddayTRPCClient,
  MockMiddayCostDatabase,
  type IFCViewerRef,
  type IFCModel,
  type BIMTeam,
  type CameraState,
  type Measurement,
} from "@midday/bim";
import { Card, CardContent, CardHeader, CardTitle } from "@midday/ui/card";
import { Button } from "@midday/ui/button";
import { Icons } from "@midday/ui/icons";
import { Badge } from "@midday/ui/badge";
import { useToast } from "@midday/ui/use-toast";

interface BIMViewerIntegrationProps {
  projectId: string;
  modelUrl?: string;
  currency?: string;
  onModelLoaded?: (model: IFCModel) => void;
  onInvoiceGenerated?: (invoiceId: string) => void;
  className?: string;
}

/**
 * Integrated BIM Viewer Component for Midday Dashboard
 * Combines 3D viewing, QTO calculation, and Midday integration
 */
export function BIMViewerIntegration({
  projectId,
  modelUrl,
  currency = "USD",
  onModelLoaded,
  onInvoiceGenerated,
  className = "",
}: BIMViewerIntegrationProps) {
  const { toast } = useToast();
  const viewerRef = useRef<IFCViewerRef>(null);
  
  // BIM state
  const [model, setModel] = useState<IFCModel | null>(null);
  const [currentCamera, setCurrentCamera] = useState<CameraState>();
  const [isLoading, setIsLoading] = useState(false);
  
  // Selection and interaction state
  const { 
    selectedElements, 
    selectElement, 
    deselectElement,
    toggleElement,
    clearSelection 
  } = useElementSelection();
  
  // QTO and costs
  const {
    results: qtoResults,
    isCalculating,
    calculate: calculateQTO,
    exportResults
  } = useQTO();

  // Teams (mock data for demo)
  const [teams, setTeams] = useState<BIMTeam[]>([]);

  // Measurements
  const [measurements, setMeasurements] = useState<Measurement[]>([]);
  const [isMeasuring, setIsMeasuring] = useState(false);

  // Viewer configuration
  const [viewerConfig, setViewerConfig] = useState({
    enableSelection: true,
    enableHighlighting: true,
    enableMeasurement: false,
    enableSectioning: false,
    backgroundColor: "#f8f9fa",
    gridVisible: true,
  });

  // Midday integration setup
  const trpcClient = new MockMiddayTRPCClient();
  const costDatabase = new MockMiddayCostDatabase();
  const bimIntegration = new BIMMiddayIntegration(trpcClient, projectId);
  const qtoCalculator = new MiddayQTOCalculator(costDatabase, projectId, currency);

  // Event handlers
  const handleModelLoaded = useCallback(async (loadedModel: IFCModel) => {
    setModel(loadedModel);
    onModelLoaded?.(loadedModel);
    
    try {
      setIsLoading(true);
      
      // Auto-calculate QTO when model loads
      await calculateQTO(loadedModel.elements);
      
      // Register model with Midday
      await bimIntegration.registerBIMModel(loadedModel, modelUrl || "");
      
      toast({
        title: "Model loaded successfully",
        description: `${loadedModel.elements.length} elements loaded and QTO calculated`,
      });
      
      setIsLoading(false);
    } catch (error) {
      console.error("Failed to integrate model with Midday:", error);
      toast({
        title: "Integration failed",
        description: "Model loaded but Midday integration failed",
        variant: "destructive",
      });
      setIsLoading(false);
    }
  }, [calculateQTO, bimIntegration, modelUrl, onModelLoaded, toast]);

  const handleElementSelected = useCallback((guids: string[]) => {
    if (guids.length === 1) {
      toggleElement(guids[0]);
    }
  }, [toggleElement]);

  const handleTeamAssignment = useCallback((teamId: string, elementGuids: string[]) => {
    setTeams(prev => prev.map(team => 
      team.id === teamId
        ? { ...team, assignedElements: [...team.assignedElements, ...elementGuids] }
        : team
    ));
    clearSelection();
    
    toast({
      title: "Elements assigned",
      description: `${elementGuids.length} elements assigned to team`,
    });
  }, [clearSelection, toast]);

  const handleCameraCapture = useCallback((teamId: string, camera: CameraState) => {
    setTeams(prev => prev.map(team =>
      team.id === teamId
        ? {
            ...team,
            cameraPosition: {
              position: camera.position,
              target: camera.target,
              up: camera.up,
            }
          }
        : team
    ));
    
    toast({
      title: "Camera saved",
      description: "Camera position saved for team",
    });
  }, [toast]);

  const generateInvoice = useCallback(async () => {
    if (!qtoResults.length) {
      toast({
        title: "No QTO data",
        description: "Load a model and calculate QTO first",
        variant: "destructive",
      });
      return;
    }

    try {
      const invoice = await bimIntegration.createQTOInvoice(
        qtoResults,
        `BIM QTO - ${model?.name}`,
        new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
      );

      onInvoiceGenerated?.(invoice.id);
      
      toast({
        title: "Invoice created",
        description: `Invoice ${invoice.id} created from BIM data`,
      });
    } catch (error) {
      console.error("Failed to generate invoice:", error);
      toast({
        title: "Invoice generation failed",
        description: error instanceof Error ? error.message : "Unknown error",
        variant: "destructive",
      });
    }
  }, [qtoResults, model, bimIntegration, onInvoiceGenerated, toast]);

  const exportQTO = useCallback((format: 'csv' | 'json' | 'excel') => {
    try {
      exportResults(format);
      toast({
        title: "QTO exported",
        description: `QTO data exported as ${format.toUpperCase()}`,
      });
    } catch (error) {
      toast({
        title: "Export failed",
        description: error instanceof Error ? error.message : "Unknown error",
        variant: "destructive",
      });
    }
  }, [exportResults, toast]);

  return (
    <div className={`flex h-full ${className}`}>
      {/* Main 3D Viewer */}
      <div className="flex-1 relative">
        <IFCViewer
          ref={viewerRef}
          modelUrl={modelUrl}
          config={viewerConfig}
          onElementSelected={handleElementSelected}
          onElementHighlighted={(guids) => {
            console.log("Elements highlighted:", guids);
          }}
          onCameraChanged={setCurrentCamera}
          onModelLoaded={handleModelLoaded}
          className="w-full h-full"
        />

        {/* Viewer Toolbar */}
        <ViewerToolbar
          onCameraReset={() => viewerRef.current?.setCameraState({
            position: [10, 10, 10],
            target: [0, 0, 0],
            up: [0, 0, 1],
            zoom: 1,
          })}
          onScreenshot={() => {
            const screenshot = viewerRef.current?.takeScreenshot();
            if (screenshot) {
              const link = document.createElement("a");
              link.download = `bim-screenshot-${Date.now()}.png`;
              link.href = screenshot;
              link.click();
              
              toast({
                title: "Screenshot saved",
                description: "3D view screenshot downloaded",
              });
            }
          }}
          onMeasureToggle={setIsMeasuring}
          onSectionToggle={(enabled) => {
            setViewerConfig(prev => ({ ...prev, enableSectioning: enabled }));
          }}
          onIsolateSelected={() => {
            if (selectedElements.length > 0) {
              // Implementation for isolating selected elements
              toast({
                title: "Elements isolated",
                description: `${selectedElements.length} elements isolated`,
              });
            }
          }}
          onShowAll={() => {
            viewerRef.current?.clearSelection();
            viewerRef.current?.clearHighlights();
            clearSelection();
          }}
          onViewMode={(mode) => {
            console.log("View mode changed:", mode);
          }}
          config={viewerConfig}
          onConfigChange={(updates) => {
            setViewerConfig(prev => ({ ...prev, ...updates }));
          }}
          selectedElementsCount={selectedElements.length}
        />

        {/* Measurement Tool */}
        <MeasurementTool
          measurements={measurements}
          onMeasurementAdd={(measurement) => {
            setMeasurements(prev => [
              ...prev,
              { ...measurement, id: Date.now().toString() }
            ]);
          }}
          onMeasurementDelete={(id) => {
            setMeasurements(prev => prev.filter(m => m.id !== id));
          }}
          onMeasurementUpdate={(id, updates) => {
            setMeasurements(prev => prev.map(m =>
              m.id === id ? { ...m, ...updates } : m
            ));
          }}
          isActive={isMeasuring}
          onActiveChange={setIsMeasuring}
        />

        {/* Loading Overlay */}
        {(isLoading || isCalculating) && (
          <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 shadow-lg">
              <div className="flex items-center space-x-3">
                <Icons.Spinner className="h-6 w-6 animate-spin" />
                <span className="text-gray-700">
                  {isCalculating ? "Calculating QTO..." : "Processing model..."}
                </span>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Right Sidebar - BIM Controls */}
      <div className="w-80 bg-white border-l flex flex-col overflow-hidden">
        {/* Model Info */}
        <div className="p-4 border-b">
          <div className="flex items-center justify-between mb-2">
            <h3 className="font-medium text-gray-900">BIM Model</h3>
            {model && (
              <Badge variant="secondary">
                {model.elements.length} elements
              </Badge>
            )}
          </div>
          
          {model ? (
            <div className="text-sm text-gray-600">
              <p>{model.name}</p>
              <p className="text-xs text-gray-500">{model.properties.schema}</p>
            </div>
          ) : (
            <p className="text-sm text-gray-500">
              {modelUrl ? "Loading model..." : "No model specified"}
            </p>
          )}
        </div>

        {/* Element List */}
        <div className="flex-1 overflow-hidden">
          {model && (
            <ElementList
              elements={model.elements}
              selectedElements={selectedElements}
              onElementSelect={(guid, selected) => {
                if (selected) {
                  selectElement(guid);
                } else {
                  deselectElement(guid);
                }
              }}
              onElementHighlight={(guid) => {
                viewerRef.current?.highlightElements([guid]);
              }}
            />
          )}
        </div>

        {/* QTO Summary */}
        {qtoResults.length > 0 && (
          <div className="border-t p-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Quantity Take-Off</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Items:</span>
                    <span>{qtoResults.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Total Cost:</span>
                    <span className="font-medium">
                      {new Intl.NumberFormat("en-US", {
                        style: "currency",
                        currency,
                      }).format(
                        qtoResults.reduce((sum, result) => sum + (result.cost || 0), 0)
                      )}
                    </span>
                  </div>
                </div>
                
                <div className="flex space-x-2 mt-3">
                  <Button
                    onClick={generateInvoice}
                    size="sm"
                    className="flex-1"
                  >
                    Create Invoice
                  </Button>
                  
                  <Button
                    onClick={() => exportQTO('csv')}
                    size="sm"
                    variant="outline"
                  >
                    Export
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
}