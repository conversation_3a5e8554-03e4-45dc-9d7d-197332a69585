"use client";

import { useState } from "react";
import { ElementSelector } from "./element-selector";
import { useTRPC } from "@/trpc/client";
import { But<PERSON> } from "@midday/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from "@midday/ui/dialog";
import { Icons } from "@midday/ui/icons";
import { toast } from "@midday/ui/use-toast";
import { useMutation, useQueryClient } from "@tanstack/react-query";

type Props = {
  teamId: string;
  teamName: string;
  projectId: string;
  currentAssignments?: string[];
  open: boolean;
  onOpenChange: (open: boolean) => void;
};

export function TeamAssignmentModal({
  teamId,
  teamName,
  projectId,
  currentAssignments = [],
  open,
  onOpenChange,
}: Props) {
  const [selectedGuids, setSelectedGuids] = useState<string[]>(currentAssignments);
  const [highlightedGuids, setHighlightedGuids] = useState<string[]>([]);

  const trpc = useTRPC();
  const queryClient = useQueryClient();

  const assignElementsMutation = useMutation(
    trpc.teamBimAssignments.assignElementsToTeam.mutationOptions({
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: trpc.teamBimAssignments.getBimTeamsByProject.queryKey(),
        });
        toast({
          title: "Elements assigned",
          description: `Successfully assigned ${selectedGuids.length} elements to ${teamName}`,
        });
        onOpenChange(false);
      },
      onError: () => {
        toast({
          variant: "destructive",
          title: "Assignment failed",
          description: "Failed to assign elements to the team. Please try again.",
        });
      },
    }),
  );

  const saveCameraMutation = useMutation(
    trpc.teamBimAssignments.saveCameraPosition.mutationOptions({
      onSuccess: () => {
        toast({
          title: "Camera saved",
          description: "Current 3D view saved for this team",
        });
      },
    }),
  );

  const handleSaveAssignments = () => {
    assignElementsMutation.mutate({
      teamId,
      ifcGuids: selectedGuids,
      replaceExisting: true,
    });
  };

  const handleHighlight = (guids: string[]) => {
    setHighlightedGuids(guids);
    // In a real implementation, this would communicate with the 3D viewer
    console.log('Highlighting elements in 3D viewer:', guids);
    
    toast({
      title: "Elements highlighted",
      description: `Highlighted ${guids.length} element${guids.length !== 1 ? 's' : ''} in 3D view`,
    });
  };

  const handleSaveCamera = () => {
    // In a real implementation, this would get the current camera position from the 3D viewer
    const mockCameraPosition = {
      position: [10.5, 15.2, 8.7],
      target: [0, 0, 0],
      up: [0, 1, 0],
    };

    saveCameraMutation.mutate({
      teamId,
      camera: mockCameraPosition,
    });
  };

  const handleCancel = () => {
    setSelectedGuids(currentAssignments);
    setHighlightedGuids([]);
    onOpenChange(false);
  };

  const hasChanges = selectedGuids.length !== currentAssignments.length ||
    !selectedGuids.every(guid => currentAssignments.includes(guid));

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>Assign Elements to {teamName}</DialogTitle>
          <DialogDescription>
            Select BIM elements to assign to this construction team. 
            Assigned elements will be highlighted for this team's view.
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 grid grid-cols-1 lg:grid-cols-3 gap-6 min-h-0">
          {/* Element Selector */}
          <div className="lg:col-span-2">
            <ElementSelector
              projectId={projectId}
              selectedGuids={selectedGuids}
              onSelectionChange={setSelectedGuids}
              onHighlight={handleHighlight}
              maxHeight="500px"
            />
          </div>

          {/* 3D Viewer Placeholder */}
          <div className="lg:col-span-1">
            <div className="h-full min-h-[400px] bg-muted/20 rounded-lg border-2 border-dashed border-muted flex flex-col items-center justify-center p-6">
              <Icons.Cube className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="font-medium mb-2">3D Model Viewer</h3>
              <p className="text-sm text-muted-foreground text-center mb-4">
                The 3D BIM viewer will be displayed here when integrated
              </p>
              
              {highlightedGuids.length > 0 && (
                <div className="text-center">
                  <div className="text-sm font-medium text-primary mb-2">
                    Currently Highlighted:
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {highlightedGuids.length} element{highlightedGuids.length !== 1 ? 's' : ''}
                  </div>
                </div>
              )}

              <div className="mt-4 w-full space-y-2">
                <Button
                  size="sm"
                  variant="outline"
                  className="w-full"
                  onClick={handleSaveCamera}
                  disabled={saveCameraMutation.isPending}
                >
                  {saveCameraMutation.isPending ? (
                    <Icons.Loader className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <Icons.Camera className="mr-2 h-4 w-4" />
                  )}
                  Save View
                </Button>
                
                <div className="text-xs text-muted-foreground text-center">
                  Save current camera position for this team
                </div>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter className="flex justify-between">
          <div className="flex items-center text-sm text-muted-foreground">
            {selectedGuids.length} element{selectedGuids.length !== 1 ? 's' : ''} selected
            {hasChanges && (
              <span className="text-primary ml-2">• Unsaved changes</span>
            )}
          </div>
          
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleCancel}>
              Cancel
            </Button>
            <Button
              onClick={handleSaveAssignments}
              disabled={assignElementsMutation.isPending || !hasChanges}
            >
              {assignElementsMutation.isPending ? (
                <Icons.Loader className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Icons.Save className="mr-2 h-4 w-4" />
              )}
              Save Assignments
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}