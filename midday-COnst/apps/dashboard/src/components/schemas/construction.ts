import { z } from "zod";

// Construction Project Types
export const constructionProjectTypes = [
  "residential",
  "commercial",
  "institutional",
  "mixed_use",
  "industrial",
  "heavy_civil",
] as const;

export const constructionProjectTypeEnum = z.enum(constructionProjectTypes);

export type ConstructionProjectType = z.infer<typeof constructionProjectTypeEnum>;

// Construction Team Roles
export const constructionTeamRoles = [
  "bim_manager",
  "structural",
  "mep",
  "architect",
  "contractor",
] as const;

export const constructionTeamRoleEnum = z.enum(constructionTeamRoles);

export type ConstructionTeamRole = z.infer<typeof constructionTeamRoleEnum>;

// Construction Status Types (extends tracker status)
export const constructionStatusTypes = [
  "pending",
  "in_progress",
  "active",
  "completed",
  "finished",
] as const;

export const constructionStatusEnum = z.enum(constructionStatusTypes);

export type ConstructionStatus = z.infer<typeof constructionStatusEnum>;

// BIM File Types
export const bimFileTypes = ["ifc", "frag", "json"] as const;

export const bimFileTypeEnum = z.enum(bimFileTypes);

export type BimFileType = z.infer<typeof bimFileTypeEnum>;

// Construction Project Schema
export const constructionProjectSchema = z.object({
  id: z.string().uuid().optional(),
  name: z.string().min(1, "Project name is required"),
  description: z.string().optional(),
  projectType: constructionProjectTypeEnum.optional(),
  projectAddress: z.string().optional(),
  projectFinishDate: z.date().optional(),
  projectProgress: z.number().min(0).max(100).optional().default(0),
  status: constructionStatusEnum.optional().default("pending"),
  customerId: z.string().uuid().nullable().optional(),
  estimate: z.number().optional(),
  billable: z.boolean().optional().default(false),
  rate: z.number().min(1).optional(),
  currency: z.string().optional(),
  tags: z
    .array(
      z.object({
        id: z.string().uuid(),
        value: z.string(),
      }),
    )
    .optional(),
});

export type ConstructionProject = z.infer<typeof constructionProjectSchema>;

// BIM Team Assignment Schema
export const bimTeamAssignmentSchema = z.object({
  id: z.string().uuid().optional(),
  teamName: z.string().min(1, "Team name is required"),
  teamRole: constructionTeamRoleEnum,
  teamDescription: z.string().optional(),
  contactName: z.string().optional(),
  contactPhone: z.string().optional(),
  projectId: z.string().uuid(),
  ifcGuids: z.array(z.string()).optional(),
  camera: z.object({
    position: z.array(z.number()).length(3),
    target: z.array(z.number()).length(3),
    up: z.array(z.number()).length(3).optional(),
  }).optional(),
});

export type BimTeamAssignment = z.infer<typeof bimTeamAssignmentSchema>;

// BIM File Schema
export const bimFileSchema = z.object({
  id: z.string().uuid().optional(),
  projectId: z.string().uuid(),
  fileName: z.string().min(1, "File name is required"),
  fileType: bimFileTypeEnum,
  fileSize: z.number().positive("File size must be positive"),
  mimeType: z.string(),
  storagePath: z.string(),
  publicUrl: z.string().url().nullable().optional(),
  uploadStatus: z.enum(["pending", "completed", "failed"]).default("pending"),
  processedAt: z.date().nullable().optional(),
  metadata: z.record(z.unknown()).nullable().optional(),
});

export type BimFile = z.infer<typeof bimFileSchema>;

// Validation Helpers
export const validateProjectType = (type: string): type is ConstructionProjectType => {
  return constructionProjectTypes.includes(type as ConstructionProjectType);
};

export const validateTeamRole = (role: string): role is ConstructionTeamRole => {
  return constructionTeamRoles.includes(role as ConstructionTeamRole);
};

export const validateConstructionStatus = (status: string): status is ConstructionStatus => {
  return constructionStatusTypes.includes(status as ConstructionStatus);
};

export const validateBimFileType = (type: string): type is BimFileType => {
  return bimFileTypes.includes(type as BimFileType);
};

// Form Schemas (for use with react-hook-form)
export const createProjectFormSchema = constructionProjectSchema.omit({ id: true });

export const updateProjectFormSchema = constructionProjectSchema.partial();

export const createTeamFormSchema = bimTeamAssignmentSchema.omit({ id: true });

export const updateTeamFormSchema = bimTeamAssignmentSchema.partial().extend({
  id: z.string().uuid(),
});

export const uploadBimFileFormSchema = bimFileSchema.pick({
  projectId: true,
  fileName: true,
  fileType: true,
  fileSize: true,
  mimeType: true,
});

// Display Labels
export const projectTypeLabels: Record<ConstructionProjectType, string> = {
  residential: "Residential",
  commercial: "Commercial",
  institutional: "Institutional",
  mixed_use: "Mixed Use",
  industrial: "Industrial",
  heavy_civil: "Heavy Civil",
};

export const teamRoleLabels: Record<ConstructionTeamRole, string> = {
  bim_manager: "BIM Manager",
  structural: "Structural Engineer",
  mep: "MEP Engineer",
  architect: "Architect",
  contractor: "Contractor",
};

export const statusLabels: Record<ConstructionStatus, string> = {
  pending: "Pending",
  in_progress: "In Progress",
  active: "Active",
  completed: "Completed",
  finished: "Finished",
};

export const fileTypeLabels: Record<BimFileType, string> = {
  ifc: "IFC Model",
  frag: "Fragment Geometry",
  json: "Properties Data",
};

// Color mappings for UI consistency
export const projectTypeColors: Record<ConstructionProjectType, string> = {
  residential: "bg-blue-100 text-blue-800 border-blue-200",
  commercial: "bg-green-100 text-green-800 border-green-200",
  institutional: "bg-purple-100 text-purple-800 border-purple-200",
  mixed_use: "bg-orange-100 text-orange-800 border-orange-200",
  industrial: "bg-gray-100 text-gray-800 border-gray-200",
  heavy_civil: "bg-red-100 text-red-800 border-red-200",
};

export const statusColors: Record<ConstructionStatus, string> = {
  pending: "bg-yellow-100 text-yellow-800 border-yellow-200",
  in_progress: "bg-blue-100 text-blue-800 border-blue-200",
  active: "bg-green-100 text-green-800 border-green-200",
  completed: "bg-purple-100 text-purple-800 border-purple-200",
  finished: "bg-gray-100 text-gray-800 border-gray-200",
};

// Utility functions
export const formatProjectType = (type: ConstructionProjectType): string => {
  return projectTypeLabels[type] || type.replace('_', ' ');
};

export const formatTeamRole = (role: ConstructionTeamRole): string => {
  return teamRoleLabels[role] || role.replace('_', ' ');
};

export const formatStatus = (status: ConstructionStatus): string => {
  return statusLabels[status] || status.replace('_', ' ');
};

export const getProjectProgress = (progress?: number | null): number => {
  if (progress === null || progress === undefined) return 0;
  return Math.max(0, Math.min(100, progress));
};

export const isProjectOverdue = (finishDate?: string | null): boolean => {
  if (!finishDate) return false;
  return new Date(finishDate) < new Date();
};

export const getProjectDaysRemaining = (finishDate?: string | null): number | null => {
  if (!finishDate) return null;
  const finish = new Date(finishDate);
  const now = new Date();
  const diffTime = finish.getTime() - now.getTime();
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};