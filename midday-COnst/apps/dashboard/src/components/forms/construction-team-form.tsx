"use client";

import { useZodForm } from "@/hooks/use-zod-form";
import { useTRPC } from "@/trpc/client";
import {
  constructionTeamRoleEnum,
  createTeamFormSchema,
  teamRoleLabels,
  type ConstructionTeamRole,
} from "@/components/schemas/construction";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@midday/ui/form";
import { Input } from "@midday/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@midday/ui/select";
import { SubmitButton } from "@midday/ui/submit-button";
import { Textarea } from "@midday/ui/textarea";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { z } from "zod";

type Props = {
  projectId: string;
  onSuccess?: () => void;
  onCancel?: () => void;
};

export function ConstructionTeamForm({
  projectId,
  onSuccess,
  onCancel,
}: Props) {
  const trpc = useTRPC();
  const queryClient = useQueryClient();

  const createTeamMutation = useMutation(
    trpc.teamBimAssignments.createBimTeam.mutationOptions({
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: trpc.teamBimAssignments.getBimTeamsByProject.queryKey(),
        });
        onSuccess?.();
      },
    }),
  );

  const form = useZodForm(createTeamFormSchema, {
    defaultValues: {
      teamName: "",
      teamRole: "structural" as ConstructionTeamRole,
      teamDescription: "",
      contactName: "",
      contactPhone: "",
      projectId,
      ifcGuids: [],
    },
  });

  const onSubmit = (data: z.infer<typeof createTeamFormSchema>) => {
    createTeamMutation.mutate({
      ...data,
      projectId,
    });
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="teamName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Team Name</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  placeholder="e.g., Foundation Crew Alpha"
                  autoComplete="off"
                  autoFocus
                />
              </FormControl>
              <FormDescription>
                A descriptive name for this construction team.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="teamRole"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Team Role</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select team specialty" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {Object.entries(teamRoleLabels).map(([value, label]) => (
                    <SelectItem key={value} value={value}>
                      {label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormDescription>
                The construction specialty of this team.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="teamDescription"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea
                  {...field}
                  placeholder="Describe the team's responsibilities and scope of work..."
                  className="resize-none"
                  rows={3}
                />
              </FormControl>
              <FormDescription>
                Optional details about the team's specific responsibilities.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="contactName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Contact Person</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder="John Smith"
                    autoComplete="name"
                  />
                </FormControl>
                <FormDescription>
                  Primary contact for this team.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="contactPhone"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Contact Phone</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder="+****************"
                    autoComplete="tel"
                    type="tel"
                  />
                </FormControl>
                <FormDescription>
                  Phone number for team coordination.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="flex justify-end space-x-3 pt-4">
          {onCancel && (
            <SubmitButton
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={createTeamMutation.isPending}
            >
              Cancel
            </SubmitButton>
          )}
          <SubmitButton
            type="submit"
            disabled={createTeamMutation.isPending}
            isSubmitting={createTeamMutation.isPending}
          >
            Create Team
          </SubmitButton>
        </div>
      </form>
    </Form>
  );
}