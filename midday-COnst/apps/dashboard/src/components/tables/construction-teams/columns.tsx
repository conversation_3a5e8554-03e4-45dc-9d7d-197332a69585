"use client";

import { useTRPC } from "@/trpc/client";
import {
  formatTeamRole,
  type ConstructionTeamRole,
} from "@/components/schemas/construction";
import type { RouterOutputs } from "@api/trpc/routers/_app";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@midday/ui/alert-dialog";
import { Avatar, AvatarFallback } from "@midday/ui/avatar";
import { Badge } from "@midday/ui/badge";
import { Button } from "@midday/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@midday/ui/dropdown-menu";
import { Icons } from "@midday/ui/icons";
import { toast } from "@midday/ui/use-toast";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import type { ColumnDef } from "@tanstack/react-table";
import { MoreHorizontal } from "lucide-react";

type TeamData = RouterOutputs["teamBimAssignments"]["getBimTeamsByProject"]["data"][number];

export const constructionTeamColumns: ColumnDef<TeamData>[] = [
  {
    id: "team",
    accessorKey: "teamName",
    header: "Team",
    cell: ({ row }) => {
      const team = row.original;
      
      return (
        <div className="flex items-center space-x-3">
          <Avatar className="h-8 w-8">
            <AvatarFallback className="text-xs font-medium">
              {team.teamName.charAt(0).toUpperCase()}
            </AvatarFallback>
          </Avatar>
          <div className="flex flex-col">
            <span className="font-medium text-sm">{team.teamName}</span>
            <span className="text-xs text-muted-foreground">
              {formatTeamRole(team.teamRole)}
            </span>
          </div>
        </div>
      );
    },
  },
  {
    id: "role",
    accessorKey: "teamRole",
    header: "Role",
    cell: ({ row }) => {
      const role = row.original.teamRole;
      return (
        <Badge variant="outline" className="capitalize">
          {formatTeamRole(role)}
        </Badge>
      );
    },
  },
  {
    id: "contact",
    accessorKey: "contactName",
    header: "Contact",
    cell: ({ row }) => {
      const team = row.original;
      
      if (!team.contactName) {
        return <span className="text-muted-foreground">-</span>;
      }
      
      return (
        <div className="flex flex-col">
          <span className="text-sm">{team.contactName}</span>
          {team.contactPhone && (
            <span className="text-xs text-muted-foreground">
              {team.contactPhone}
            </span>
          )}
        </div>
      );
    },
  },
  {
    id: "elements",
    accessorKey: "numberOfElements",
    header: "Elements",
    cell: ({ row }) => {
      const count = row.original.numberOfElements;
      return (
        <div className="flex items-center space-x-2">
          <Badge variant="secondary">
            {count} element{count !== 1 ? 's' : ''}
          </Badge>
          {count > 0 && (
            <Icons.Layers className="h-4 w-4 text-muted-foreground" />
          )}
        </div>
      );
    },
  },
  {
    id: "description",
    accessorKey: "teamDescription",
    header: "Description",
    cell: ({ row }) => {
      const description = row.original.teamDescription;
      
      if (!description) {
        return <span className="text-muted-foreground">-</span>;
      }
      
      return (
        <span className="text-sm max-w-[200px] truncate block">
          {description}
        </span>
      );
    },
  },
  {
    id: "created",
    accessorKey: "createdAt",
    header: "Created",
    cell: ({ row }) => {
      const date = new Date(row.original.createdAt);
      return (
        <span className="text-sm text-muted-foreground">
          {date.toLocaleDateString(undefined, {
            month: 'short',
            day: 'numeric',
            year: 'numeric'
          })}
        </span>
      );
    },
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const trpc = useTRPC();
      const queryClient = useQueryClient();
      const team = row.original;

      const deleteTeamMutation = useMutation(
        trpc.teamBimAssignments.deleteBimTeam.mutationOptions({
          onSuccess: () => {
            queryClient.invalidateQueries({
              queryKey: trpc.teamBimAssignments.getBimTeamsByProject.queryKey(),
            });
            toast({
              title: "Team deleted",
              description: "The construction team has been removed from the project.",
            });
          },
          onError: () => {
            toast({
              variant: "destructive",
              title: "Error",
              description: "Failed to delete the team. Please try again.",
            });
          },
        }),
      );

      const highlightElementsMutation = useMutation(
        trpc.teamBimAssignments.highlightTeamElements.mutationOptions({
          onSuccess: (data) => {
            // In a real implementation, this would trigger 3D highlighting
            console.log('Highlighting elements:', data.ifcGuids);
            toast({
              title: "Elements highlighted",
              description: `Highlighted ${data.ifcGuids.length} elements for ${team.teamName}`,
            });
          },
        }),
      );

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <MoreHorizontal className="h-4 w-4" />
              <span className="sr-only">Open menu</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem
              onClick={() => highlightElementsMutation.mutate({ teamId: team.id })}
              disabled={team.numberOfElements === 0}
            >
              <Icons.Eye className="mr-2 h-4 w-4" />
              View Elements
            </DropdownMenuItem>
            
            <DropdownMenuItem
              onClick={() => {
                // TODO: Open element assignment modal
                console.log('Assign elements to', team.teamName);
              }}
            >
              <Icons.Layers className="mr-2 h-4 w-4" />
              Assign Elements
            </DropdownMenuItem>
            
            <DropdownMenuItem
              onClick={() => {
                // TODO: Open camera management modal  
                console.log('Manage camera for', team.teamName);
              }}
              disabled={!team.camera}
            >
              <Icons.Camera className="mr-2 h-4 w-4" />
              Manage View
            </DropdownMenuItem>
            
            <DropdownMenuSeparator />
            
            <DropdownMenuItem
              onClick={() => {
                // TODO: Open edit modal
                console.log('Edit', team.teamName);
              }}
            >
              <Icons.Edit className="mr-2 h-4 w-4" />
              Edit Team
            </DropdownMenuItem>
            
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <DropdownMenuItem 
                  className="text-destructive"
                  onSelect={(e) => e.preventDefault()}
                >
                  <Icons.Trash className="mr-2 h-4 w-4" />
                  Delete Team
                </DropdownMenuItem>
              </AlertDialogTrigger>
              
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Delete Team</AlertDialogTitle>
                  <AlertDialogDescription>
                    Are you sure you want to delete "{team.teamName}"? 
                    This will remove all element assignments and camera positions. 
                    This action cannot be undone.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={() => deleteTeamMutation.mutate({ teamId: team.id })}
                    disabled={deleteTeamMutation.isPending}
                    className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                  >
                    {deleteTeamMutation.isPending ? (
                      <Icons.Loader className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      <Icons.Trash className="mr-2 h-4 w-4" />
                    )}
                    Delete Team
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];