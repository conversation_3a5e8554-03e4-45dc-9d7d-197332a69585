"use client";

import { useState, useRef } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@midday/ui/button";
import { Icons } from "@midday/ui/icons";
import { Badge } from "@midday/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@midday/ui/card";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@midday/ui/tabs";
import { Progress } from "@midday/ui/progress";
import { Separator } from "@midday/ui/separator";
import { BIMViewerIntegration } from "@/components/bim/bim-viewer-integration";
import { 
  QTOCalculator,
  useElementSelection,
  useQTO,
  type IFCModel,
  type BIMTeam,
  type Measurement,
} from "@midday/bim";
import { format } from "date-fns";

// Mock project data - in real implementation, this would come from tRPC query
const mockProject = {
  id: "project_123",
  name: "Downtown Office Complex",
  projectType: "commercial" as const,
  projectAddress: "123 Main St, Downtown, NY 10001",
  projectFinishDate: new Date("2024-12-31"),
  projectProgress: 45,
  status: "active" as const,
  currency: "USD",
  estimatedCost: 2500000,
  actualCost: 1125000,
  teamId: "team_456",
  assignedUserId: "user_789",
  // BIM-specific fields
  ifcRoute: "/storage/projects/project_123/model.ifc",
  fragRoute: "/storage/projects/project_123/model.frag",
  jsonRoute: "/storage/projects/project_123/properties.json",
  description: "Modern 15-story office building with retail space on ground floor",
  createdAt: new Date("2024-01-15"),
  updatedAt: new Date("2024-03-15"),
};

interface ProjectDetailViewProps {
  projectId: string;
}

export function ProjectDetailView({ projectId }: ProjectDetailViewProps) {
  const router = useRouter();
  
  // BIM state
  const [model, setModel] = useState<IFCModel | null>(null);
  const [invoiceGenerated, setInvoiceGenerated] = useState<string | null>(null);
  
  // Selection state for QTO calculator
  const { selectedElements } = useElementSelection();
  
  // Teams and assignments (mock data)
  const [teams, setTeams] = useState<BIMTeam[]>([
    {
      id: "team_structural",
      name: "Structural Team",
      role: "structural",
      description: "Responsible for structural elements",
      contactName: "John Doe",
      contactPhone: "+****************",
      assignedElements: [],
    },
    {
      id: "team_mep",
      name: "MEP Team",
      role: "mep",
      description: "Mechanical, Electrical, Plumbing",
      contactName: "Jane Smith", 
      contactPhone: "+****************",
      assignedElements: [],
    },
  ]);

  // Measurements
  const [measurements, setMeasurements] = useState<Measurement[]>([]);

  // Event handlers
  const handleModelLoaded = (loadedModel: IFCModel) => {
    setModel(loadedModel);
    console.log("Model loaded in project detail view:", loadedModel.name);
  };

  const handleInvoiceGenerated = (invoiceId: string) => {
    setInvoiceGenerated(invoiceId);
    console.log("Invoice generated:", invoiceId);
  };

  const getProjectTypeColor = (type: string) => {
    const colors = {
      residential: "bg-green-100 text-green-800",
      commercial: "bg-blue-100 text-blue-800",
      institutional: "bg-purple-100 text-purple-800",
      mixed_use: "bg-orange-100 text-orange-800",
      industrial: "bg-gray-100 text-gray-800",
      heavy_civil: "bg-red-100 text-red-800",
    };
    return colors[type as keyof typeof colors] || "bg-gray-100 text-gray-800";
  };

  const getStatusColor = (status: string) => {
    const colors = {
      active: "bg-green-100 text-green-800",
      completed: "bg-blue-100 text-blue-800",
      on_hold: "bg-yellow-100 text-yellow-800",
      cancelled: "bg-red-100 text-red-800",
    };
    return colors[status as keyof typeof colors] || "bg-gray-100 text-gray-800";
  };

  const progressColor = mockProject.projectProgress >= 80 
    ? "bg-green-500" 
    : mockProject.projectProgress >= 50 
    ? "bg-blue-500" 
    : "bg-orange-500";

  return (
    <div className="flex h-full flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b bg-white">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.back()}
            className="p-0"
          >
            <Icons.ChevronLeft className="h-4 w-4" />
          </Button>
          
          <div>
            <div className="flex items-center space-x-3">
              <h1 className="text-2xl font-semibold text-gray-900">
                {mockProject.name}
              </h1>
              <Badge className={getProjectTypeColor(mockProject.projectType)}>
                {mockProject.projectType.replace("_", " ")}
              </Badge>
              <Badge className={getStatusColor(mockProject.status)}>
                {mockProject.status}
              </Badge>
            </div>
            <p className="text-sm text-gray-500 mt-1">
              {mockProject.projectAddress}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-3">
          <div className="text-right">
            <div className="text-lg font-semibold">
              ${mockProject.actualCost.toLocaleString()} / ${mockProject.estimatedCost.toLocaleString()}
            </div>
            <div className="text-sm text-gray-500">
              Spent / Budget
            </div>
          </div>
          
          <Separator orientation="vertical" className="h-8" />
          
          <div className="text-right">
            <div className="text-lg font-semibold">
              {mockProject.projectProgress}%
            </div>
            <div className="text-sm text-gray-500">
              Complete
            </div>
          </div>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="px-6 py-3 bg-gray-50 border-b">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700">Project Progress</span>
          <span className="text-sm text-gray-500">
            Due: {format(mockProject.projectFinishDate, "MMM dd, yyyy")}
          </span>
        </div>
        <Progress 
          value={mockProject.projectProgress} 
          className={`h-2 ${progressColor}`}
        />
      </div>

      {/* Main Content */}
      <div className="flex-1 flex overflow-hidden">
        <Tabs defaultValue="bim" className="flex flex-1 flex-col">
          {/* Tab Navigation */}
          <div className="border-b bg-white px-6">
            <TabsList className="grid w-full max-w-md grid-cols-4">
              <TabsTrigger value="bim">BIM Viewer</TabsTrigger>
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="costs">Costs</TabsTrigger>
              <TabsTrigger value="teams">Teams</TabsTrigger>
            </TabsList>
          </div>

          {/* BIM Viewer Tab */}
          <TabsContent value="bim" className="flex-1 flex m-0">
            <BIMViewerIntegration
              projectId={projectId}
              modelUrl={mockProject.ifcRoute}
              currency={mockProject.currency}
              onModelLoaded={handleModelLoaded}
              onInvoiceGenerated={handleInvoiceGenerated}
              className="flex-1"
            />
          </TabsContent>

          {/* Overview Tab */}
          <TabsContent value="overview" className="flex-1 p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Project Info</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Type:</span>
                    <span className="text-sm font-medium">{mockProject.projectType}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Status:</span>
                    <span className="text-sm font-medium">{mockProject.status}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Progress:</span>
                    <span className="text-sm font-medium">{mockProject.projectProgress}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Due Date:</span>
                    <span className="text-sm font-medium">
                      {format(mockProject.projectFinishDate, "MMM dd, yyyy")}
                    </span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>BIM Model</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Model:</span>
                    <span className="text-sm font-medium">{model?.name || "No model loaded"}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Elements:</span>
                    <span className="text-sm font-medium">{model?.elements.length || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Schema:</span>
                    <span className="text-sm font-medium">{model?.properties.schema || "N/A"}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Teams:</span>
                    <span className="text-sm font-medium">{teams.length}</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Measurements</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Total:</span>
                    <span className="text-sm font-medium">{measurements.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Distance:</span>
                    <span className="text-sm font-medium">
                      {measurements.filter(m => m.type === "distance").length}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Area:</span>
                    <span className="text-sm font-medium">
                      {measurements.filter(m => m.type === "area").length}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Volume:</span>
                    <span className="text-sm font-medium">
                      {measurements.filter(m => m.type === "volume").length}
                    </span>
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card className="mt-6">
              <CardHeader>
                <CardTitle>Description</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600">{mockProject.description}</p>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Costs Tab */}
          <TabsContent value="costs" className="flex-1 p-6">
            {model && (
              <QTOCalculator
                model={model}
                selectedElements={selectedElements}
                onResultsChange={(results) => {
                  console.log("QTO results updated:", results);
                }}
                costDatabase={{
                  "wall_concrete": 150.0,
                  "column_steel": 800.0,
                  "beam_steel": 600.0,
                  "slab_concrete": 120.0,
                }}
                currency={mockProject.currency}
              />
            )}
          </TabsContent>

          {/* Teams Tab */}
          <TabsContent value="teams" className="flex-1 p-6">
            <div className="space-y-6">
              {teams.map((team) => (
                <Card key={team.id}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle>{team.name}</CardTitle>
                      <Badge variant="secondary">
                        {team.role.replace("_", " ")}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm text-gray-500">Contact</p>
                        <p className="font-medium">{team.contactName}</p>
                        <p className="text-sm text-gray-600">{team.contactPhone}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">Assigned Elements</p>
                        <p className="font-medium">{team.assignedElements.length}</p>
                        <p className="text-sm text-gray-600">{team.description}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>

      {/* Status Bar */}
      <div className="bg-white border-t px-6 py-2 text-sm text-gray-600">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <span>Project: {projectId}</span>
            <span>Model: {model ? model.name : "No model loaded"}</span>
            <span>Elements: {model ? model.elements.length : 0}</span>
          </div>
          
          <div className="flex items-center space-x-4">
            <span>Teams: {teams.length}</span>
            <span>Measurements: {measurements.length}</span>
            {invoiceGenerated && (
              <span className="text-green-600">
                Invoice: {invoiceGenerated}
              </span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}