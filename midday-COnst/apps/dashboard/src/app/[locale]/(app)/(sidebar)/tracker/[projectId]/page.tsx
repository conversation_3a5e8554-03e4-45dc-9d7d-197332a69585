import { Suspense } from "react";
import { notFound } from "next/navigation";
import { ProjectDetailView } from "./project-detail-view";
import { ProjectDetailSkeleton } from "./project-detail-skeleton";

interface ProjectDetailPageProps {
  params: {
    locale: string;
    projectId: string;
  };
}

export default function ProjectDetailPage({ params }: ProjectDetailPageProps) {
  return (
    <div className="flex h-full flex-col">
      <Suspense fallback={<ProjectDetailSkeleton />}>
        <ProjectDetailView projectId={params.projectId} />
      </Suspense>
    </div>
  );
}

export function generateMetadata({ params }: ProjectDetailPageProps) {
  return {
    title: `Project Details - ${params.projectId}`,
  };
}