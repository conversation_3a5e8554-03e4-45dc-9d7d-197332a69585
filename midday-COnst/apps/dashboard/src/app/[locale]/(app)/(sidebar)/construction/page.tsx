import { ChartSelectors } from "@/components/charts/chart-selectors";
import { Charts } from "@/components/charts/charts";
import { EmptyState } from "@/components/charts/empty-state";
import { ConstructionWidgets } from "@/components/widgets/construction-widgets";
import { defaultPeriod } from "@/components/widgets/spending/data";
import { loadMetricsParams } from "@/hooks/use-metrics-params";
import { HydrateClient, batchPrefetch, trpc } from "@/trpc/server";
import { getQueryClient } from "@/trpc/server";
import type { Metadata } from "next";
import type { SearchParams } from "nuqs";

export const metadata: Metadata = {
  title: "Construction Dashboard | Midday",
  description: "Construction project management and BIM integration dashboard",
};

type Props = {
  searchParams: Promise<SearchParams>;
};

export default async function ConstructionDashboard(props: Props) {
  const queryClient = getQueryClient();
  const searchParams = await props.searchParams;
  const { from, to, currency } = loadMetricsParams(searchParams);

  // Prefetch standard financial data
  batchPrefetch([
    trpc.invoice.get.queryOptions({ pageSize: 10 }),
    trpc.invoice.paymentStatus.queryOptions(),
    trpc.metrics.expense.queryOptions({
      from,
      to,
      currency: currency ?? undefined,
    }),
    trpc.metrics.profit.queryOptions({
      from,
      to,
      currency: currency ?? undefined,
    }),
    trpc.metrics.burnRate.queryOptions({
      from,
      to,
      currency: currency ?? undefined,
    }),
    trpc.metrics.runway.queryOptions({
      from,
      to,
      currency: currency ?? undefined,
    }),
    trpc.bankAccounts.balances.queryOptions(),
    trpc.documents.get.queryOptions({ pageSize: 10 }),
    trpc.metrics.spending.queryOptions({
      from: defaultPeriod.from,
      to: defaultPeriod.to,
      currency: currency ?? undefined,
    }),
    trpc.transactions.get.queryOptions({
      pageSize: 15,
    }),
    // Construction-specific data
    trpc.projects.get.queryOptions({ pageSize: 20 }),
  ]);

  // Load the data for the first visible chart
  await Promise.all([
    queryClient.fetchQuery(
      trpc.bankAccounts.get.queryOptions({
        enabled: true,
      }),
    ),
    queryClient.fetchQuery(
      trpc.metrics.revenue.queryOptions({
        from,
        to,
        currency: currency ?? undefined,
      }),
    ),
  ]);

  return (
    <HydrateClient>
      <div>
        {/* Financial Charts Section */}
        <div className="h-[530px] mb-4">
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-2xl font-semibold text-gray-900">
              Construction Dashboard
            </h1>
          </div>
          
          <ChartSelectors />

          <div className="mt-8 relative">
            <EmptyState />
            <Charts />
          </div>
        </div>

        {/* Construction-Specific Widgets */}
        <ConstructionWidgets />
      </div>
    </HydrateClient>
  );
}