import { Skeleton } from "@midday/ui/skeleton";
import { <PERSON>, CardContent, CardHeader } from "@midday/ui/card";

export function ProjectDetailSkeleton() {
  return (
    <div className="flex h-full flex-col">
      {/* Header Skeleton */}
      <div className="flex items-center justify-between p-6 border-b bg-white">
        <div className="flex items-center space-x-4">
          <Skeleton className="h-8 w-8" />
          <div>
            <div className="flex items-center space-x-3">
              <Skeleton className="h-8 w-64" />
              <Skeleton className="h-6 w-20" />
              <Skeleton className="h-6 w-16" />
            </div>
            <Skeleton className="h-4 w-48 mt-2" />
          </div>
        </div>

        <div className="flex items-center space-x-3">
          <div className="text-right">
            <Skeleton className="h-6 w-32" />
            <Skeleton className="h-4 w-24 mt-1" />
          </div>
          <div className="text-right">
            <Skeleton className="h-6 w-16" />
            <Skeleton className="h-4 w-20 mt-1" />
          </div>
        </div>
      </div>

      {/* Progress Bar Skeleton */}
      <div className="px-6 py-3 bg-gray-50 border-b">
        <div className="flex items-center justify-between mb-2">
          <Skeleton className="h-4 w-32" />
          <Skeleton className="h-4 w-24" />
        </div>
        <Skeleton className="h-2 w-full" />
      </div>

      {/* Main Content Skeleton */}
      <div className="flex-1 flex overflow-hidden">
        {/* Tab Navigation Skeleton */}
        <div className="flex flex-1 flex-col">
          <div className="border-b bg-white px-6 py-2">
            <div className="flex space-x-2">
              <Skeleton className="h-10 w-24" />
              <Skeleton className="h-10 w-24" />
              <Skeleton className="h-10 w-24" />
              <Skeleton className="h-10 w-24" />
            </div>
          </div>

          {/* Content Area Skeleton */}
          <div className="flex-1 flex overflow-hidden">
            {/* Main Viewer Area */}
            <div className="flex-1 relative bg-gray-100">
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center">
                  <Skeleton className="h-16 w-16 rounded-full mx-auto mb-4" />
                  <Skeleton className="h-6 w-48 mb-2" />
                  <Skeleton className="h-4 w-32" />
                </div>
              </div>

              {/* Toolbar Skeleton */}
              <div className="absolute top-4 left-4">
                <div className="bg-white rounded-lg shadow border p-2 space-y-2">
                  <Skeleton className="h-8 w-8" />
                  <Skeleton className="h-8 w-8" />
                  <Skeleton className="h-8 w-8" />
                  <Skeleton className="h-8 w-8" />
                </div>
              </div>
            </div>

            {/* Right Sidebar Skeleton */}
            <div className="w-80 bg-white border-l flex flex-col">
              {/* Element List Skeleton */}
              <div className="flex-1 p-4">
                <div className="mb-4">
                  <Skeleton className="h-6 w-32 mb-2" />
                  <div className="flex space-x-2">
                    <Skeleton className="h-8 w-20" />
                    <Skeleton className="h-8 w-24" />
                  </div>
                </div>

                <div className="space-y-3">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <div key={i} className="border rounded p-3">
                      <div className="flex items-center justify-between">
                        <div>
                          <Skeleton className="h-4 w-24 mb-1" />
                          <Skeleton className="h-3 w-16" />
                        </div>
                        <Skeleton className="h-4 w-4" />
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Team Assignment Skeleton */}
              <div className="h-1/3 border-t p-4">
                <Skeleton className="h-6 w-32 mb-4" />
                <div className="space-y-3">
                  {Array.from({ length: 2 }).map((_, i) => (
                    <Card key={i}>
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <Skeleton className="h-5 w-24" />
                          <Skeleton className="h-5 w-16" />
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-2 gap-2">
                          <div>
                            <Skeleton className="h-3 w-16 mb-1" />
                            <Skeleton className="h-4 w-20" />
                          </div>
                          <div>
                            <Skeleton className="h-3 w-20 mb-1" />
                            <Skeleton className="h-4 w-8" />
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}