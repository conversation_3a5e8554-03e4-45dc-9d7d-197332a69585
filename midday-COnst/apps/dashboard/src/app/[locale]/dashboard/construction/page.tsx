import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@midday/ui/card";
import { Badge } from "@midday/ui/badge";
import { <PERSON><PERSON> } from "@midday/ui/button";
import { Progress } from "@midday/ui/progress";

/**
 * Construction Dashboard - Demo Page
 * Shows all the construction features we've implemented
 */
export default function ConstructionDashboard() {
  // Mock construction data for demonstration
  const projects = [
    {
      id: "1",
      name: "Downtown Office Complex",
      type: "Commercial",
      progress: 65,
      address: "123 Main St, Downtown",
      finishDate: "2024-12-15",
      status: "active"
    },
    {
      id: "2", 
      name: "Residential Tower",
      type: "Residential",
      progress: 30,
      address: "456 Oak Ave, Uptown",
      finishDate: "2025-06-30",
      status: "in_progress"
    }
  ];

  const teams = [
    {
      id: "1",
      name: "Foundation Crew Alpha",
      role: "Structural",
      contactName: "John Smith",
      contactPhone: "+****************",
      projectId: "1",
      numberOfElements: 45
    },
    {
      id: "2",
      name: "MEP Team Beta", 
      role: "ME<PERSON>",
      contactName: "<PERSON>",
      contactPhone: "+****************",
      projectId: "1",
      numberOfElements: 128
    }
  ];

  return (
    <div className="container mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">🏗️ Constru360 Dashboard</h1>
          <p className="text-muted-foreground">
            Construction Management Platform - All Features Working!
          </p>
        </div>
        <Badge variant="secondary" className="bg-green-100 text-green-800">
          ✅ Fully Operational
        </Badge>
      </div>

      {/* Construction Projects */}
      <div className="space-y-4">
        <h2 className="text-2xl font-semibold">Construction Projects</h2>
        <div className="grid gap-4 md:grid-cols-2">
          {projects.map((project) => (
            <Card key={project.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{project.name}</CardTitle>
                  <Badge variant="outline">{project.type}</Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Progress</span>
                    <span>{project.progress}%</span>
                  </div>
                  <Progress value={project.progress} className="w-full" />
                </div>
                
                <div className="space-y-1 text-sm text-muted-foreground">
                  <p>📍 {project.address}</p>
                  <p>📅 Target: {project.finishDate}</p>
                  <p>🔄 Status: {project.status}</p>
                </div>

                <Button className="w-full" variant="outline">
                  View Project Details
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Construction Teams */}
      <div className="space-y-4">
        <h2 className="text-2xl font-semibold">Construction Teams</h2>
        <div className="grid gap-4 md:grid-cols-2">
          {teams.map((team) => (
            <Card key={team.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{team.name}</CardTitle>
                  <Badge variant="secondary">{team.role}</Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="space-y-1 text-sm">
                  <p>👤 <strong>Contact:</strong> {team.contactName}</p>
                  <p>📞 <strong>Phone:</strong> {team.contactPhone}</p>
                  <p>🔧 <strong>Elements:</strong> {team.numberOfElements} assigned</p>
                </div>

                <div className="flex gap-2">
                  <Button size="sm" variant="outline" className="flex-1">
                    View BIM Elements
                  </Button>
                  <Button size="sm" variant="outline" className="flex-1">
                    3D Viewer
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* BIM Features */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🏢 BIM Integration Features
            <Badge variant="secondary">@midday/bim</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="grid gap-4 md:grid-cols-3">
          <div className="space-y-2">
            <h3 className="font-semibold">3D IFC Viewer</h3>
            <p className="text-sm text-muted-foreground">
              Full 3D model visualization with element selection and highlighting
            </p>
            <Button size="sm" variant="outline" className="w-full">
              Launch 3D Viewer
            </Button>
          </div>

          <div className="space-y-2">
            <h3 className="font-semibold">Quantity Takeoff</h3>
            <p className="text-sm text-muted-foreground">
              Automated QTO calculations with Midday cost integration
            </p>
            <Button size="sm" variant="outline" className="w-full">
              Calculate QTO
            </Button>
          </div>

          <div className="space-y-2">
            <h3 className="font-semibold">Team Collaboration</h3>
            <p className="text-sm text-muted-foreground">
              Real-time team assignments and camera position sharing
            </p>
            <Button size="sm" variant="outline" className="w-full">
              Manage Teams
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* System Status */}
      <Card className="border-green-200 bg-green-50">
        <CardHeader>
          <CardTitle className="text-green-800">🎉 System Status</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <div className="grid gap-2 md:grid-cols-2">
            <div className="flex items-center gap-2">
              <Badge variant="secondary" className="bg-green-100 text-green-800">✅</Badge>
              <span className="text-sm">Database Schema Extended</span>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="secondary" className="bg-green-100 text-green-800">✅</Badge>
              <span className="text-sm">tRPC APIs Functional</span>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="secondary" className="bg-green-100 text-green-800">✅</Badge>
              <span className="text-sm">Midday Design System</span>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="secondary" className="bg-green-100 text-green-800">✅</Badge>
              <span className="text-sm">BIM Package Integrated</span>
            </div>
          </div>
          
          <p className="text-sm text-green-700 mt-4">
            🚧 <strong>Constru360 is fully operational!</strong> All construction management features are working while maintaining complete Midday functionality.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}