CREATE TYPE "public"."bim_file_status" AS ENUM('pending', 'completed', 'failed');--> statement-breakpoint
CREATE TYPE "public"."bim_file_type" AS ENUM('ifc', 'frag', 'json');--> statement-breakpoint
CREATE TYPE "public"."construction_project_type" AS ENUM('residential', 'commercial', 'institutional', 'mixed_use', 'industrial', 'heavy_civil');--> statement-breakpoint
CREATE TYPE "public"."construction_team_role" AS ENUM('bim_manager', 'structural', 'mep', 'architect', 'contractor');--> statement-breakpoint
ALTER TYPE "public"."trackerStatus" ADD VALUE 'pending';--> statement-breakpoint
ALTER TYPE "public"."trackerStatus" ADD VALUE 'active';--> statement-breakpoint
ALTER TYPE "public"."trackerStatus" ADD VALUE 'finished';--> statement-breakpoint
CREATE TABLE "api_keys" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"key_encrypted" text NOT NULL,
	"name" text NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"user_id" uuid NOT NULL,
	"team_id" uuid NOT NULL,
	"key_hash" text,
	"scopes" text[] DEFAULT '{}'::text[] NOT NULL,
	"last_used_at" timestamp with time zone,
	CONSTRAINT "api_keys_key_unique" UNIQUE("key_hash")
);
--> statement-breakpoint
CREATE TABLE "bim_files" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"team_id" uuid NOT NULL,
	"project_id" uuid NOT NULL,
	"file_name" text NOT NULL,
	"file_type" "bim_file_type" NOT NULL,
	"file_size" bigint NOT NULL,
	"mime_type" text NOT NULL,
	"storage_path" text NOT NULL,
	"public_url" text,
	"upload_status" "bim_file_status" DEFAULT 'pending' NOT NULL,
	"processed_at" timestamp with time zone,
	"metadata" jsonb
);
--> statement-breakpoint
CREATE TABLE "short_links" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"short_id" text NOT NULL,
	"url" text NOT NULL,
	"team_id" uuid NOT NULL,
	"user_id" uuid NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	CONSTRAINT "short_links_short_id_unique" UNIQUE("short_id")
);
--> statement-breakpoint
ALTER TABLE "short_links" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "team_bim_assignments" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"team_id" uuid NOT NULL,
	"project_id" uuid NOT NULL,
	"team_name" text NOT NULL,
	"team_role" "construction_team_role" NOT NULL,
	"team_description" text,
	"contact_name" text,
	"contact_phone" text,
	"ifc_guids" jsonb,
	"camera" jsonb,
	CONSTRAINT "team_bim_assignments_unique" UNIQUE("team_id","project_id","team_name")
);
--> statement-breakpoint
ALTER TABLE "team_bim_assignments" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "auth.users" (
	"instance_id" uuid,
	"id" uuid NOT NULL,
	"aud" varchar(255),
	"role" varchar(255),
	"email" varchar(255),
	"encrypted_password" varchar(255),
	"email_confirmed_at" timestamp with time zone,
	"invited_at" timestamp with time zone,
	"confirmation_token" varchar(255),
	"confirmation_sent_at" timestamp with time zone,
	"recovery_token" varchar(255),
	"recovery_sent_at" timestamp with time zone,
	"email_change_token_new" varchar(255),
	"email_change" varchar(255),
	"email_change_sent_at" timestamp with time zone,
	"last_sign_in_at" timestamp with time zone,
	"raw_app_meta_data" jsonb,
	"raw_user_meta_data" jsonb,
	"is_super_admin" boolean,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"phone" text DEFAULT null::character varying,
	"phone_confirmed_at" timestamp with time zone,
	"phone_change" text DEFAULT ''::character varying,
	"phone_change_token" varchar(255) DEFAULT ''::character varying,
	"phone_change_sent_at" timestamp with time zone,
	"confirmed_at" timestamp with time zone GENERATED ALWAYS AS (LEAST(email_confirmed_at, phone_confirmed_at)) STORED,
	"email_change_token_current" varchar(255) DEFAULT ''::character varying,
	"email_change_confirm_status" smallint DEFAULT 0,
	"banned_until" timestamp with time zone,
	"reauthentication_token" varchar(255) DEFAULT ''::character varying,
	"reauthentication_sent_at" timestamp with time zone,
	"is_sso_user" boolean DEFAULT false NOT NULL,
	"deleted_at" timestamp with time zone,
	"is_anonymous" boolean DEFAULT false NOT NULL,
	CONSTRAINT "users_pkey" PRIMARY KEY("id"),
	CONSTRAINT "users_phone_key" UNIQUE("phone"),
	CONSTRAINT "confirmation_token_idx" UNIQUE("confirmation_token"),
	CONSTRAINT "email_change_token_current_idx" UNIQUE("email_change_token_current"),
	CONSTRAINT "email_change_token_new_idx" UNIQUE("email_change_token_new"),
	CONSTRAINT "reauthentication_token_idx" UNIQUE("reauthentication_token"),
	CONSTRAINT "recovery_token_idx" UNIQUE("recovery_token"),
	CONSTRAINT "users_email_partial_key" UNIQUE("email")
);
--> statement-breakpoint
ALTER TABLE "invoice_comments" DISABLE ROW LEVEL SECURITY;--> statement-breakpoint
ALTER TABLE "users" DROP CONSTRAINT "users_id_fkey";
--> statement-breakpoint
ALTER TABLE "bank_connections" ALTER COLUMN "provider" SET DATA TYPE text;--> statement-breakpoint
DROP TYPE "public"."bank_providers";--> statement-breakpoint
CREATE TYPE "public"."bank_providers" AS ENUM('gocardless', 'plaid', 'teller', 'enablebanking');--> statement-breakpoint
ALTER TABLE "bank_connections" ALTER COLUMN "provider" SET DATA TYPE "public"."bank_providers" USING "provider"::"public"."bank_providers";--> statement-breakpoint
ALTER TABLE "transactions" ALTER COLUMN "amount" SET DATA TYPE numeric(10, 2);--> statement-breakpoint
ALTER TABLE "transactions" ALTER COLUMN "balance" SET DATA TYPE numeric(10, 2);--> statement-breakpoint
ALTER TABLE "transactions" ALTER COLUMN "fts_vector" SET NOT NULL;--> statement-breakpoint
ALTER TABLE "transactions" drop column "fts_vector";--> statement-breakpoint
ALTER TABLE "transactions" ADD COLUMN "fts_vector" "tsvector" GENERATED ALWAYS AS (
				to_tsvector(
					'english',
					(
						(COALESCE(name, ''::text) || ' '::text) || COALESCE(description, ''::text)
					)
				)
			) STORED NOT NULL;--> statement-breakpoint
ALTER TABLE "tracker_entries" ALTER COLUMN "rate" SET DATA TYPE numeric(10, 2);--> statement-breakpoint
ALTER TABLE "bank_accounts" ALTER COLUMN "balance" SET DATA TYPE numeric(10, 2);--> statement-breakpoint
ALTER TABLE "invoices" ALTER COLUMN "amount" SET DATA TYPE numeric(10, 2);--> statement-breakpoint
ALTER TABLE "invoices" ALTER COLUMN "fts" SET NOT NULL;--> statement-breakpoint
ALTER TABLE "invoices" drop column "fts";--> statement-breakpoint
ALTER TABLE "invoices" ADD COLUMN "fts" "tsvector" GENERATED ALWAYS AS (
        to_tsvector(
          'english',
          (
            (COALESCE((amount)::text, ''::text) || ' '::text) || COALESCE(invoice_number, ''::text)
          )
        )
      ) STORED NOT NULL;--> statement-breakpoint
ALTER TABLE "invoices" ALTER COLUMN "vat" SET DATA TYPE numeric(10, 2);--> statement-breakpoint
ALTER TABLE "invoices" ALTER COLUMN "tax" SET DATA TYPE numeric(10, 2);--> statement-breakpoint
ALTER TABLE "invoices" ALTER COLUMN "discount" SET DATA TYPE numeric(10, 2);--> statement-breakpoint
ALTER TABLE "invoices" ALTER COLUMN "subtotal" SET DATA TYPE numeric(10, 2);--> statement-breakpoint
ALTER TABLE "customers" ALTER COLUMN "fts" SET NOT NULL;--> statement-breakpoint
ALTER TABLE "customers" drop column "fts";--> statement-breakpoint
ALTER TABLE "customers" ADD COLUMN "fts" "tsvector" GENERATED ALWAYS AS (
				to_tsvector(
					'english'::regconfig,
					COALESCE(name, ''::text) || ' ' ||
					COALESCE(contact, ''::text) || ' ' ||
					COALESCE(phone, ''::text) || ' ' ||
					COALESCE(email, ''::text) || ' ' ||
					COALESCE(address_line_1, ''::text) || ' ' ||
					COALESCE(address_line_2, ''::text) || ' ' ||
					COALESCE(city, ''::text) || ' ' ||
					COALESCE(state, ''::text) || ' ' ||
					COALESCE(zip, ''::text) || ' ' ||
					COALESCE(country, ''::text)
				)
			) STORED NOT NULL;--> statement-breakpoint
ALTER TABLE "exchange_rates" ALTER COLUMN "rate" SET DATA TYPE numeric(10, 2);--> statement-breakpoint
ALTER TABLE "bank_connections" ALTER COLUMN "provider" SET NOT NULL;--> statement-breakpoint
ALTER TABLE "user_invites" ALTER COLUMN "code" SET DEFAULT 'nanoid(24)';--> statement-breakpoint
ALTER TABLE "teams" ALTER COLUMN "inbox_id" SET DEFAULT 'generate_inbox(10)';--> statement-breakpoint
ALTER TABLE "documents" ALTER COLUMN "fts" SET NOT NULL;--> statement-breakpoint
ALTER TABLE "invoice_templates" ALTER COLUMN "tax_rate" SET DATA TYPE numeric(10, 2);--> statement-breakpoint
ALTER TABLE "invoice_templates" ALTER COLUMN "vat_rate" SET DATA TYPE numeric(10, 2);--> statement-breakpoint
ALTER TABLE "users" ALTER COLUMN "time_format" SET DEFAULT 24;--> statement-breakpoint
ALTER TABLE "tracker_projects" ALTER COLUMN "rate" SET DATA TYPE numeric(10, 2);--> statement-breakpoint
ALTER TABLE "tracker_projects" ALTER COLUMN "fts" SET NOT NULL;--> statement-breakpoint
ALTER TABLE "tracker_projects" drop column "fts";--> statement-breakpoint
ALTER TABLE "tracker_projects" ADD COLUMN "fts" "tsvector" GENERATED ALWAYS AS (
          to_tsvector(
            'english'::regconfig,
            (
              (COALESCE(name, ''::text) || ' '::text) || COALESCE(description, ''::text)
            )
          )
        ) STORED NOT NULL;--> statement-breakpoint
ALTER TABLE "inbox" ALTER COLUMN "amount" SET DATA TYPE numeric(10, 2);--> statement-breakpoint
ALTER TABLE "inbox" ALTER COLUMN "fts" SET NOT NULL;--> statement-breakpoint
ALTER TABLE "inbox" ALTER COLUMN "base_amount" SET DATA TYPE numeric(10, 2);--> statement-breakpoint
ALTER TABLE "inbox" ALTER COLUMN "tax_amount" SET DATA TYPE numeric(10, 2);--> statement-breakpoint
ALTER TABLE "inbox" ALTER COLUMN "tax_rate" SET DATA TYPE numeric(10, 2);--> statement-breakpoint
ALTER TABLE "transaction_categories" ALTER COLUMN "team_id" DROP DEFAULT;--> statement-breakpoint
ALTER TABLE "transaction_categories" ALTER COLUMN "slug" DROP NOT NULL;--> statement-breakpoint
ALTER TABLE "transactions" ADD COLUMN "baseAmount" numeric(10, 2);--> statement-breakpoint
ALTER TABLE "transactions" ADD COLUMN "counterparty_name" text;--> statement-breakpoint
ALTER TABLE "transactions" ADD COLUMN "taxRate" numeric(10, 2);--> statement-breakpoint
ALTER TABLE "transactions" ADD COLUMN "tax_type" text;--> statement-breakpoint
ALTER TABLE "bank_accounts" ADD COLUMN "baseBalance" numeric(10, 2);--> statement-breakpoint
ALTER TABLE "customers" ADD COLUMN "billingEmail" text;--> statement-breakpoint
ALTER TABLE "teams" ADD COLUMN "country_code" text;--> statement-breakpoint
ALTER TABLE "invoice_templates" ADD COLUMN "send_copy" boolean;--> statement-breakpoint
ALTER TABLE "tracker_projects" ADD COLUMN "project_type" "construction_project_type";--> statement-breakpoint
ALTER TABLE "tracker_projects" ADD COLUMN "project_address" text;--> statement-breakpoint
ALTER TABLE "tracker_projects" ADD COLUMN "project_finish_date" timestamp with time zone;--> statement-breakpoint
ALTER TABLE "tracker_projects" ADD COLUMN "project_progress" smallint DEFAULT 0;--> statement-breakpoint
ALTER TABLE "tracker_projects" ADD COLUMN "frag_route" text;--> statement-breakpoint
ALTER TABLE "tracker_projects" ADD COLUMN "json_route" text;--> statement-breakpoint
ALTER TABLE "transaction_categories" ADD COLUMN "tax_rate" numeric(10, 2);--> statement-breakpoint
ALTER TABLE "transaction_categories" ADD COLUMN "tax_type" text;--> statement-breakpoint
ALTER TABLE "transaction_categories" ADD COLUMN "parent_id" uuid;--> statement-breakpoint
ALTER TABLE "api_keys" ADD CONSTRAINT "api_keys_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "api_keys" ADD CONSTRAINT "api_keys_team_id_fkey" FOREIGN KEY ("team_id") REFERENCES "public"."teams"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "bim_files" ADD CONSTRAINT "bim_files_team_id_fkey" FOREIGN KEY ("team_id") REFERENCES "public"."teams"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "bim_files" ADD CONSTRAINT "bim_files_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."tracker_projects"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "short_links" ADD CONSTRAINT "short_links_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "short_links" ADD CONSTRAINT "short_links_team_id_fkey" FOREIGN KEY ("team_id") REFERENCES "public"."teams"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "team_bim_assignments" ADD CONSTRAINT "team_bim_assignments_team_id_fkey" FOREIGN KEY ("team_id") REFERENCES "public"."teams"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "team_bim_assignments" ADD CONSTRAINT "team_bim_assignments_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."tracker_projects"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "api_keys_key_idx" ON "api_keys" USING btree ("key_hash" text_ops);--> statement-breakpoint
CREATE INDEX "api_keys_user_id_idx" ON "api_keys" USING btree ("user_id" uuid_ops);--> statement-breakpoint
CREATE INDEX "api_keys_team_id_idx" ON "api_keys" USING btree ("team_id" uuid_ops);--> statement-breakpoint
CREATE INDEX "bim_files_team_id_idx" ON "bim_files" USING btree ("team_id" uuid_ops);--> statement-breakpoint
CREATE INDEX "bim_files_project_id_idx" ON "bim_files" USING btree ("project_id" uuid_ops);--> statement-breakpoint
CREATE INDEX "bim_files_file_type_idx" ON "bim_files" USING btree ("file_type" enum_ops);--> statement-breakpoint
CREATE INDEX "short_links_short_id_idx" ON "short_links" USING btree ("short_id" text_ops);--> statement-breakpoint
CREATE INDEX "short_links_team_id_idx" ON "short_links" USING btree ("team_id" uuid_ops);--> statement-breakpoint
CREATE INDEX "short_links_user_id_idx" ON "short_links" USING btree ("user_id" uuid_ops);--> statement-breakpoint
CREATE INDEX "team_bim_assignments_team_id_idx" ON "team_bim_assignments" USING btree ("team_id" uuid_ops);--> statement-breakpoint
CREATE INDEX "team_bim_assignments_project_id_idx" ON "team_bim_assignments" USING btree ("project_id" uuid_ops);--> statement-breakpoint
CREATE INDEX "users_instance_id_email_idx" ON "auth.users" USING btree ("instance_id",lower((email)::text));--> statement-breakpoint
CREATE INDEX "users_instance_id_idx" ON "auth.users" USING btree ("instance_id");--> statement-breakpoint
CREATE INDEX "users_is_anonymous_idx" ON "auth.users" USING btree ("is_anonymous");--> statement-breakpoint
ALTER TABLE "users" ADD CONSTRAINT "users_id_fkey" FOREIGN KEY ("id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "transaction_categories" ADD CONSTRAINT "transaction_categories_parent_id_fkey" FOREIGN KEY ("parent_id") REFERENCES "public"."transaction_categories"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "transaction_categories_parent_id_idx" ON "transaction_categories" USING btree ("parent_id" uuid_ops);--> statement-breakpoint
ALTER TABLE "transactions" DROP COLUMN "base_amount";--> statement-breakpoint
ALTER TABLE "bank_accounts" DROP COLUMN "base_balance";--> statement-breakpoint
ALTER TABLE "transaction_categories" DROP COLUMN "vat";--> statement-breakpoint
CREATE POLICY "Short links can be created by a member of the team" ON "short_links" AS PERMISSIVE FOR INSERT TO "authenticated" WITH CHECK ((team_id IN ( SELECT private.get_teams_for_authenticated_user() AS get_teams_for_authenticated_user)));--> statement-breakpoint
CREATE POLICY "Short links can be selected by a member of the team" ON "short_links" AS PERMISSIVE FOR SELECT TO "authenticated" USING ((team_id IN ( SELECT private.get_teams_for_authenticated_user() AS get_teams_for_authenticated_user)));--> statement-breakpoint
CREATE POLICY "Short links can be updated by a member of the team" ON "short_links" AS PERMISSIVE FOR UPDATE TO "authenticated" USING ((team_id IN ( SELECT private.get_teams_for_authenticated_user() AS get_teams_for_authenticated_user)));--> statement-breakpoint
CREATE POLICY "Short links can be deleted by a member of the team" ON "short_links" AS PERMISSIVE FOR DELETE TO "authenticated" USING ((team_id IN ( SELECT private.get_teams_for_authenticated_user() AS get_teams_for_authenticated_user)));--> statement-breakpoint
CREATE POLICY "BIM assignments can be created by a member of the team" ON "team_bim_assignments" AS PERMISSIVE FOR INSERT TO "authenticated" WITH CHECK ((team_id IN ( SELECT private.get_teams_for_authenticated_user() AS get_teams_for_authenticated_user)));--> statement-breakpoint
CREATE POLICY "BIM assignments can be deleted by a member of the team" ON "team_bim_assignments" AS PERMISSIVE FOR DELETE TO "authenticated";--> statement-breakpoint
CREATE POLICY "BIM assignments can be selected by a member of the team" ON "team_bim_assignments" AS PERMISSIVE FOR SELECT TO "authenticated";--> statement-breakpoint
CREATE POLICY "BIM assignments can be updated by a member of the team" ON "team_bim_assignments" AS PERMISSIVE FOR UPDATE TO "authenticated";--> statement-breakpoint
DROP TYPE "public"."bankProviders";