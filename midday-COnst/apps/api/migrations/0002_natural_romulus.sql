ALTER TABLE "teams" ADD COLUMN "team_role" "construction_team_role";--> statement-breakpoint
ALTER TABLE "teams" ADD COLUMN "contact_name" text;--> statement-breakpoint
ALTER TABLE "teams" ADD COLUMN "contact_phone" text;--> statement-breakpoint
ALTER TABLE "teams" ADD COLUMN "camera_position" jsonb;--> statement-breakpoint
ALTER TABLE "teams" ADD COLUMN "ifc_guids" jsonb;--> statement-breakpoint
ALTER TABLE "teams" ADD COLUMN "team_description" text;--> statement-breakpoint
ALTER TABLE "teams" ADD COLUMN "project_id" uuid;--> statement-breakpoint
ALTER TABLE "teams" ADD CONSTRAINT "teams_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."tracker_projects"("id") ON DELETE set null ON UPDATE cascade;