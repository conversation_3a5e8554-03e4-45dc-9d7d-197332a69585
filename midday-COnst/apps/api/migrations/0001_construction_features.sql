-- Migration: Add Construction Features to Midday
-- Created for Constru360 transformation

-- Add new construction project types enum
CREATE TYPE "construction_project_type" AS ENUM(
  'residential',
  'commercial', 
  'institutional',
  'mixed_use',
  'industrial',
  'heavy_civil'
);

-- Add new construction team roles enum  
CREATE TYPE "construction_team_role" AS ENUM(
  'bim_manager',
  'structural',
  'mep',
  'architect', 
  'contractor'
);

-- Extend tracker status enum with construction statuses
ALTER TYPE "trackerStatus" ADD VALUE 'pending';
ALTER TYPE "trackerStatus" ADD VALUE 'active'; 
ALTER TYPE "trackerStatus" ADD VALUE 'finished';

-- Add construction fields to tracker_projects table
ALTER TABLE "tracker_projects" ADD COLUMN "project_type" "construction_project_type";
ALTER TABLE "tracker_projects" ADD COLUMN "project_address" text;
ALTER TABLE "tracker_projects" ADD COLUMN "project_finish_date" timestamp with time zone;
ALTER TABLE "tracker_projects" ADD COLUMN "project_progress" smallint DEFAULT 0;
ALTER TABLE "tracker_projects" ADD COLUMN "frag_route" text;
ALTER TABLE "tracker_projects" ADD COLUMN "json_route" text;

-- Create bim_file_status enum
CREATE TYPE "bim_file_status" AS ENUM('pending', 'completed', 'failed');

-- Create bim_file_type enum  
CREATE TYPE "bim_file_type" AS ENUM('ifc', 'frag', 'json');

-- Create bim_files table for BIM file storage
CREATE TABLE "bim_files" (
  "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
  "created_at" timestamp with time zone DEFAULT now() NOT NULL,
  "updated_at" timestamp with time zone DEFAULT now() NOT NULL,
  "team_id" uuid NOT NULL,
  "project_id" uuid NOT NULL,
  "file_name" text NOT NULL,
  "file_type" "bim_file_type" NOT NULL,
  "file_size" bigint NOT NULL,
  "mime_type" text NOT NULL,
  "storage_path" text NOT NULL,
  "public_url" text,
  "upload_status" "bim_file_status" DEFAULT 'pending' NOT NULL,
  "processed_at" timestamp with time zone,
  "metadata" jsonb
);

-- Create indexes for bim_files
CREATE INDEX "bim_files_team_id_idx" ON "bim_files" USING btree ("team_id");
CREATE INDEX "bim_files_project_id_idx" ON "bim_files" USING btree ("project_id");
CREATE INDEX "bim_files_file_type_idx" ON "bim_files" USING btree ("file_type");

-- Add foreign key constraints for bim_files
ALTER TABLE "bim_files" ADD CONSTRAINT "bim_files_team_id_fkey" FOREIGN KEY ("team_id") REFERENCES "teams"("id") ON DELETE cascade ON UPDATE no action;
ALTER TABLE "bim_files" ADD CONSTRAINT "bim_files_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "tracker_projects"("id") ON DELETE cascade ON UPDATE no action;

-- Enable Row Level Security on bim_files
ALTER TABLE "bim_files" ENABLE ROW LEVEL SECURITY;

-- Add RLS policies for bim_files
CREATE POLICY "BIM files can be created by a member of the team" ON "bim_files" AS PERMISSIVE FOR INSERT TO "authenticated" WITH CHECK ((team_id IN ( SELECT private.get_teams_for_authenticated_user() AS get_teams_for_authenticated_user)));
CREATE POLICY "BIM files can be deleted by a member of the team" ON "bim_files" AS PERMISSIVE FOR DELETE TO "authenticated";
CREATE POLICY "BIM files can be selected by a member of the team" ON "bim_files" AS PERMISSIVE FOR SELECT TO "authenticated";
CREATE POLICY "BIM files can be updated by a member of the team" ON "bim_files" AS PERMISSIVE FOR UPDATE TO "authenticated";

-- Create team_bim_assignments table for BIM element assignments
CREATE TABLE "team_bim_assignments" (
  "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
  "created_at" timestamp with time zone DEFAULT now() NOT NULL,
  "team_id" uuid NOT NULL,
  "project_id" uuid NOT NULL,
  "team_name" text NOT NULL,
  "team_role" "construction_team_role" NOT NULL,
  "team_description" text,
  "contact_name" text,
  "contact_phone" text,
  "ifc_guids" jsonb,
  "camera" jsonb
);

-- Add indexes for team_bim_assignments
CREATE INDEX "team_bim_assignments_team_id_idx" ON "team_bim_assignments" USING btree ("team_id");
CREATE INDEX "team_bim_assignments_project_id_idx" ON "team_bim_assignments" USING btree ("project_id");

-- Add foreign key constraints
ALTER TABLE "team_bim_assignments" ADD CONSTRAINT "team_bim_assignments_team_id_fkey" 
  FOREIGN KEY ("team_id") REFERENCES "teams"("id") ON DELETE CASCADE;
  
ALTER TABLE "team_bim_assignments" ADD CONSTRAINT "team_bim_assignments_project_id_fkey" 
  FOREIGN KEY ("project_id") REFERENCES "tracker_projects"("id") ON DELETE CASCADE;

-- Add unique constraint to prevent duplicate team assignments per project
ALTER TABLE "team_bim_assignments" ADD CONSTRAINT "team_bim_assignments_unique" 
  UNIQUE ("team_id", "project_id", "team_name");

-- Enable Row Level Security on team_bim_assignments
ALTER TABLE "team_bim_assignments" ENABLE ROW LEVEL SECURITY;

-- Add RLS policies for team_bim_assignments
CREATE POLICY "BIM assignments can be created by a member of the team" ON "team_bim_assignments" AS PERMISSIVE FOR INSERT TO "authenticated" WITH CHECK ((team_id IN ( SELECT private.get_teams_for_authenticated_user() AS get_teams_for_authenticated_user)));

CREATE POLICY "BIM assignments can be deleted by a member of the team" ON "team_bim_assignments" AS PERMISSIVE FOR DELETE TO "authenticated";

CREATE POLICY "BIM assignments can be selected by a member of the team" ON "team_bim_assignments" AS PERMISSIVE FOR SELECT TO "authenticated";

CREATE POLICY "BIM assignments can be updated by a member of the team" ON "team_bim_assignments" AS PERMISSIVE FOR UPDATE TO "authenticated";

-- Add comments for documentation
COMMENT ON TABLE "bim_files" IS 'Stores BIM file metadata and upload information';
COMMENT ON COLUMN "bim_files"."file_type" IS 'Type of BIM file: ifc (original), frag (geometry), json (properties)';
COMMENT ON COLUMN "bim_files"."storage_path" IS 'Full storage path in Supabase storage';
COMMENT ON COLUMN "bim_files"."metadata" IS 'JSON object with file-specific metadata (IFC version, element count, etc.)';

COMMENT ON TABLE "team_bim_assignments" IS 'Stores BIM element assignments to construction teams with camera positions';
COMMENT ON COLUMN "team_bim_assignments"."ifc_guids" IS 'JSON array of assigned IFC element GUIDs';
COMMENT ON COLUMN "team_bim_assignments"."camera" IS 'JSON object storing 3D camera position {position, target, up}';
COMMENT ON COLUMN "tracker_projects"."project_type" IS 'Type of construction project';
COMMENT ON COLUMN "tracker_projects"."project_progress" IS 'Project completion percentage (0-100)';
COMMENT ON COLUMN "tracker_projects"."frag_route" IS 'Path to optimized 3D model .frag file';
COMMENT ON COLUMN "tracker_projects"."json_route" IS 'Path to IFC properties .json file';