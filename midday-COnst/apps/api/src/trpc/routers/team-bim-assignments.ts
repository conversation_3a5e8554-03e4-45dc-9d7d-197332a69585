import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "../init";
import {
  createBimTeamSchema,
  updateBimTeamElementsSchema,
  getBimTeamsByProjectSchema,
  deleteBimTeamSchema,
  bimTeamResponseSchema,
  bimTeamsResponseSchema,
} from "@api/schemas/team-bim-assignments";
import {
  createBimTeam,
  deleteBimTeam,
  getAllBimTeamsForProject,
  getBimTeamById,
  updateBimTeamElements,
} from "@api/db/queries/team-bim-assignments";

export const teamBimAssignmentsRouter = createTRPCRouter({
  createBimTeam: protectedProcedure
    .input(createBimTeamSchema)
    .output(bimTeamResponseSchema)
    .mutation(async ({ input, ctx }) => {
      const result = await createBimTeam(ctx.db, {
        teamId: ctx.teamId!,
        teamName: input.teamName,
        teamRole: input.teamRole,
        teamDescription: input.teamDescription,
        contactName: input.contactName,
        contactPhone: input.contactPhone,
        projectId: input.projectId,
        ifcGuids: input.ifcGuids,
        camera: input.camera,
      });

      return {
        id: result.id,
        teamName: result.teamName,
        teamRole: result.teamRole,
        teamDescription: result.teamDescription,
        contactName: result.contactName,
        contactPhone: result.contactPhone,
        projectId: result.projectId,
        ifcGuids: result.ifcGuids ? JSON.parse(result.ifcGuids as string) : null,
        camera: result.camera ? JSON.parse(result.camera as string) : null,
        numberOfElements: result.numberOfElements,
        createdAt: result.createdAt,
      };
    }),

  getBimTeamsByProject: protectedProcedure
    .input(getBimTeamsByProjectSchema)
    .output(bimTeamsResponseSchema)
    .query(async ({ input, ctx }) => {
      const teams = await getAllBimTeamsForProject(ctx.db, input.projectId);

      const formattedTeams = teams.map((team) => ({
        id: team.id,
        teamName: team.teamName,
        teamRole: team.teamRole,
        teamDescription: team.teamDescription,
        contactName: team.contactName,
        contactPhone: team.contactPhone,
        projectId: team.projectId,
        ifcGuids: team.ifcGuids ? JSON.parse(team.ifcGuids as string) : null,
        camera: team.camera ? JSON.parse(team.camera as string) : null,
        numberOfElements: team.numberOfElements,
        createdAt: team.createdAt,
      }));

      return {
        data: formattedTeams,
      };
    }),

  getBimTeamById: protectedProcedure
    .input(z.object({ teamId: z.string().uuid() }))
    .output(bimTeamResponseSchema)
    .query(async ({ input, ctx }) => {
      const team = await getBimTeamById(ctx.db, input.teamId);

      return {
        id: team.id,
        teamName: team.teamName,
        teamRole: team.teamRole,
        teamDescription: team.teamDescription,
        contactName: team.contactName,
        contactPhone: team.contactPhone,
        projectId: team.projectId,
        ifcGuids: team.ifcGuids ? JSON.parse(team.ifcGuids as string) : null,
        camera: team.camera ? JSON.parse(team.camera as string) : null,
        numberOfElements: team.numberOfElements,
        createdAt: team.createdAt,
      };
    }),

  updateBimTeamElements: protectedProcedure
    .input(updateBimTeamElementsSchema)
    .output(bimTeamResponseSchema)
    .mutation(async ({ input, ctx }) => {
      const result = await updateBimTeamElements(ctx.db, {
        teamId: input.teamId,
        ifcGuids: input.ifcGuids,
        camera: input.camera,
      });

      return {
        id: result.id,
        teamName: result.teamName,
        teamRole: result.teamRole,
        teamDescription: result.teamDescription,
        contactName: result.contactName,
        contactPhone: result.contactPhone,
        projectId: result.projectId,
        ifcGuids: result.ifcGuids ? JSON.parse(result.ifcGuids as string) : null,
        camera: result.camera ? JSON.parse(result.camera as string) : null,
        numberOfElements: result.numberOfElements,
        createdAt: result.createdAt,
      };
    }),

  deleteBimTeam: protectedProcedure
    .input(deleteBimTeamSchema)
    .output(z.object({ success: z.boolean(), id: z.string() }))
    .mutation(async ({ input, ctx }) => {
      const result = await deleteBimTeam(ctx.db, input.teamId);

      return {
        success: true,
        id: result.id,
      };
    }),

  // Additional utility procedures for team collaboration
  highlightTeamElements: protectedProcedure
    .input(
      z.object({
        teamId: z.string().uuid(),
        highlightColor: z.string().optional().default("#ff6b35"),
      })
    )
    .output(
      z.object({
        success: z.boolean(),
        ifcGuids: z.array(z.string()),
        camera: z
          .object({
            position: z.array(z.number()).length(3),
            target: z.array(z.number()).length(3),
            up: z.array(z.number()).length(3).optional(),
          })
          .nullable(),
      })
    )
    .query(async ({ input, ctx }) => {
      const team = await getBimTeamById(ctx.db, input.teamId);

      return {
        success: true,
        ifcGuids: team.ifcGuids ? JSON.parse(team.ifcGuids as string) : [],
        camera: team.camera ? JSON.parse(team.camera as string) : null,
      };
    }),

  assignElementsToTeam: protectedProcedure
    .input(
      z.object({
        teamId: z.string().uuid(),
        ifcGuids: z.array(z.string()),
        replaceExisting: z.boolean().default(false),
      })
    )
    .output(bimTeamResponseSchema)
    .mutation(async ({ input, ctx }) => {
      let finalIfcGuids = input.ifcGuids;

      if (!input.replaceExisting) {
        // Get existing elements and merge with new ones
        const existingTeam = await getBimTeamById(ctx.db, input.teamId);
        const existingGuids = existingTeam.ifcGuids
          ? JSON.parse(existingTeam.ifcGuids as string)
          : [];
        
        // Merge and deduplicate
        finalIfcGuids = [...new Set([...existingGuids, ...input.ifcGuids])];
      }

      // Get current camera position (keep existing)
      const existingTeam = await getBimTeamById(ctx.db, input.teamId);
      const camera = existingTeam.camera
        ? JSON.parse(existingTeam.camera as string)
        : null;

      const result = await updateBimTeamElements(ctx.db, {
        teamId: input.teamId,
        ifcGuids: finalIfcGuids,
        camera: camera || { position: [0, 0, 0], target: [0, 0, 0] },
      });

      return {
        id: result.id,
        teamName: result.teamName,
        teamRole: result.teamRole,
        teamDescription: result.teamDescription,
        contactName: result.contactName,
        contactPhone: result.contactPhone,
        projectId: result.projectId,
        ifcGuids: result.ifcGuids ? JSON.parse(result.ifcGuids as string) : null,
        camera: result.camera ? JSON.parse(result.camera as string) : null,
        numberOfElements: result.numberOfElements,
        createdAt: result.createdAt,
      };
    }),

  saveCameraPosition: protectedProcedure
    .input(
      z.object({
        teamId: z.string().uuid(),
        camera: z.object({
          position: z.array(z.number()).length(3),
          target: z.array(z.number()).length(3),
          up: z.array(z.number()).length(3).optional(),
        }),
      })
    )
    .output(z.object({ success: z.boolean() }))
    .mutation(async ({ input, ctx }) => {
      // Get current IFC GUIDs (keep existing)
      const existingTeam = await getBimTeamById(ctx.db, input.teamId);
      const ifcGuids = existingTeam.ifcGuids
        ? JSON.parse(existingTeam.ifcGuids as string)
        : [];

      await updateBimTeamElements(ctx.db, {
        teamId: input.teamId,
        ifcGuids,
        camera: input.camera,
      });

      return { success: true };
    }),
});