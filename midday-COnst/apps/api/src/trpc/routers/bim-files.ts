import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "../init";
import {
  uploadBimFileSchema,
  bimFileUploadResponseSchema,
  confirmBimFileUploadSchema,
  getBimFilesSchema,
  deleteBimFileSchema,
  bimFileResponseSchema,
  bimFilesResponseSchema,
  processBimFileSchema,
  bimFileDownloadUrlSchema,
  bimFileDownloadResponseSchema,
} from "@api/schemas/bim-files";
import {
  createBimFile,
  deleteBimFile,
  getBimFilesByProject,
  getBimFileById,
  updateBimFile,
  getBimFilesByType,
  getRelatedBimFiles,
  updateBimFileMetadata,
} from "@api/db/queries/bim-files";
import { signedUrl, remove } from "@midday/supabase/storage";
import { TRPCError } from "@trpc/server";

export const bimFilesRouter = createTRPCRouter({
  // Initiate file upload - returns signed URL for direct upload to storage
  initiateUpload: protectedProcedure
    .input(uploadBimFileSchema)
    .output(bimFileUploadResponseSchema)
    .mutation(async ({ input, ctx }) => {
      // Generate storage path: projects/{projectId}/bim/{fileType}/{fileName}
      const storagePath = `projects/${input.projectId}/bim/${input.fileType}/${Date.now()}-${input.fileName}`;
      
      // Create database record
      const fileRecord = await createBimFile(ctx.db, {
        teamId: ctx.teamId!,
        projectId: input.projectId,
        fileName: input.fileName,
        fileType: input.fileType,
        fileSize: input.fileSize,
        mimeType: input.mimeType,
        storagePath,
      });

      // Generate signed upload URL (valid for 10 minutes)
      const { data } = await signedUrl(ctx.supabase, {
        bucket: "bim-files",
        path: storagePath,
        expireIn: 600, // 10 minutes
      });

      if (!data?.signedUrl) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to generate upload URL",
        });
      }

      const expiresAt = new Date(Date.now() + 600 * 1000).toISOString();

      return {
        uploadUrl: data.signedUrl,
        fileId: fileRecord.id,
        storagePath,
        expiresAt,
      };
    }),

  // Confirm successful upload and update file status
  confirmUpload: protectedProcedure
    .input(confirmBimFileUploadSchema)
    .output(bimFileResponseSchema)
    .mutation(async ({ input, ctx }) => {
      // Get the public URL for the uploaded file
      const { data } = await ctx.supabase.storage
        .from("bim-files")
        .getPublicUrl(await getBimFileById(ctx.db, input.fileId).then(f => f.storagePath));

      const updatedFile = await updateBimFile(ctx.db, {
        id: input.fileId,
        uploadStatus: "completed",
        publicUrl: data.publicUrl,
      });

      return {
        id: updatedFile.id,
        projectId: updatedFile.projectId,
        fileName: updatedFile.fileName,
        fileType: updatedFile.fileType,
        fileSize: updatedFile.fileSize,
        mimeType: updatedFile.mimeType,
        storagePath: updatedFile.storagePath,
        publicUrl: updatedFile.publicUrl,
        uploadStatus: updatedFile.uploadStatus,
        processedAt: updatedFile.processedAt,
        metadata: updatedFile.metadata as Record<string, unknown> | null,
        createdAt: updatedFile.createdAt,
      };
    }),

  // Get all BIM files for a project
  getByProject: protectedProcedure
    .input(getBimFilesSchema)
    .output(bimFilesResponseSchema)
    .query(async ({ input, ctx }) => {
      const result = await getBimFilesByProject(ctx.db, {
        teamId: ctx.teamId!,
        projectId: input.projectId,
        fileType: input.fileType,
      });

      const formattedFiles = result.data.map((file) => ({
        id: file.id,
        projectId: file.projectId,
        fileName: file.fileName,
        fileType: file.fileType,
        fileSize: file.fileSize,
        mimeType: file.mimeType,
        storagePath: file.storagePath,
        publicUrl: file.publicUrl,
        uploadStatus: file.uploadStatus,
        processedAt: file.processedAt,
        metadata: file.metadata as Record<string, unknown> | null,
        createdAt: file.createdAt,
      }));

      return {
        data: formattedFiles,
        totalSize: result.totalSize,
        summary: result.summary,
      };
    }),

  // Get a specific BIM file by ID
  getById: protectedProcedure
    .input(z.object({ fileId: z.string().uuid() }))
    .output(bimFileResponseSchema)
    .query(async ({ input, ctx }) => {
      const file = await getBimFileById(ctx.db, input.fileId);

      return {
        id: file.id,
        projectId: file.projectId,
        fileName: file.fileName,
        fileType: file.fileType,
        fileSize: file.fileSize,
        mimeType: file.mimeType,
        storagePath: file.storagePath,
        publicUrl: file.publicUrl,
        uploadStatus: file.uploadStatus,
        processedAt: file.processedAt,
        metadata: file.metadata as Record<string, unknown> | null,
        createdAt: file.createdAt,
      };
    }),

  // Delete a BIM file
  delete: protectedProcedure
    .input(deleteBimFileSchema)
    .output(z.object({ success: z.boolean(), id: z.string() }))
    .mutation(async ({ input, ctx }) => {
      const file = await deleteBimFile(ctx.db, input.fileId);

      // Remove from storage
      await remove(ctx.supabase, {
        bucket: "bim-files",
        path: file.storagePath.split("/"),
      });

      return {
        success: true,
        id: file.id,
      };
    }),

  // Get download URL for a BIM file
  getDownloadUrl: protectedProcedure
    .input(bimFileDownloadUrlSchema)
    .output(bimFileDownloadResponseSchema)
    .mutation(async ({ input, ctx }) => {
      const file = await getBimFileById(ctx.db, input.fileId);

      const { data } = await signedUrl(ctx.supabase, {
        bucket: "bim-files",
        path: file.storagePath,
        expireIn: input.expireIn,
        options: { download: true },
      });

      if (!data?.signedUrl) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to generate download URL",
        });
      }

      const expiresAt = new Date(Date.now() + input.expireIn * 1000).toISOString();

      return {
        downloadUrl: data.signedUrl,
        fileName: file.fileName,
        fileSize: file.fileSize,
        expiresAt,
      };
    }),

  // Process IFC file to generate FRAG and JSON files
  processIfc: protectedProcedure
    .input(processBimFileSchema)
    .output(z.object({ success: z.boolean(), message: z.string() }))
    .mutation(async ({ input, ctx }) => {
      const file = await getBimFileById(ctx.db, input.fileId);

      if (file.fileType !== "ifc") {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "File is not an IFC file",
        });
      }

      if (file.uploadStatus !== "completed") {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "File upload is not completed",
        });
      }

      // In a real implementation, this would trigger a background job
      // For now, we'll just update the metadata to indicate processing started
      await updateBimFileMetadata(ctx.db, input.fileId, {
        processingStarted: new Date().toISOString(),
        generateFragments: input.generateFragments,
        generateProperties: input.generateProperties,
        optimizeForViewer: input.optimizeForViewer,
      });

      return {
        success: true,
        message: "IFC processing initiated. FRAG and JSON files will be generated.",
      };
    }),

  // Get files by type (useful for 3D viewer)
  getByType: protectedProcedure
    .input(
      z.object({
        projectId: z.string().uuid(),
        fileType: z.enum(["ifc", "frag", "json"]),
      })
    )
    .output(z.array(bimFileResponseSchema.pick({
      id: true,
      fileName: true,
      storagePath: true,
      publicUrl: true,
      fileSize: true,
      createdAt: true,
    })))
    .query(async ({ input, ctx }) => {
      const files = await getBimFilesByType(ctx.db, {
        teamId: ctx.teamId!,
        projectId: input.projectId,
        fileType: input.fileType,
      });

      return files.map((file) => ({
        id: file.id,
        fileName: file.fileName,
        storagePath: file.storagePath,
        publicUrl: file.publicUrl,
        fileSize: file.fileSize,
        createdAt: file.createdAt,
      }));
    }),

  // Get related files for an IFC file (its generated FRAG and JSON files)
  getRelatedFiles: protectedProcedure
    .input(z.object({ ifcFileId: z.string().uuid() }))
    .output(
      z.object({
        ifcFile: bimFileResponseSchema,
        fragFiles: z.array(bimFileResponseSchema.pick({
          id: true,
          fileName: true,
          storagePath: true,
          publicUrl: true,
          fileSize: true,
        })),
        jsonFiles: z.array(bimFileResponseSchema.pick({
          id: true,
          fileName: true,
          storagePath: true,
          publicUrl: true,
          fileSize: true,
        })),
      })
    )
    .query(async ({ input, ctx }) => {
      const { ifcFile, relatedFiles } = await getRelatedBimFiles(ctx.db, input.ifcFileId);

      const fragFiles = relatedFiles.filter(f => f.fileType === "frag");
      const jsonFiles = relatedFiles.filter(f => f.fileType === "json");

      return {
        ifcFile: {
          id: ifcFile.id,
          projectId: ifcFile.projectId,
          fileName: ifcFile.fileName,
          fileType: ifcFile.fileType,
          fileSize: ifcFile.fileSize,
          mimeType: ifcFile.mimeType,
          storagePath: ifcFile.storagePath,
          publicUrl: ifcFile.publicUrl,
          uploadStatus: ifcFile.uploadStatus,
          processedAt: ifcFile.processedAt,
          metadata: ifcFile.metadata as Record<string, unknown> | null,
          createdAt: ifcFile.createdAt,
        },
        fragFiles: fragFiles.map(f => ({
          id: f.id,
          fileName: f.fileName,
          storagePath: f.storagePath,
          publicUrl: f.publicUrl,
          fileSize: f.fileSize,
        })),
        jsonFiles: jsonFiles.map(f => ({
          id: f.id,
          fileName: f.fileName,
          storagePath: f.storagePath,
          publicUrl: f.publicUrl,
          fileSize: f.fileSize,
        })),
      };
    }),

  // Update file metadata (useful for processing results)
  updateMetadata: protectedProcedure
    .input(
      z.object({
        fileId: z.string().uuid(),
        metadata: z.record(z.unknown()),
      })
    )
    .output(bimFileResponseSchema)
    .mutation(async ({ input, ctx }) => {
      const updatedFile = await updateBimFileMetadata(ctx.db, input.fileId, input.metadata);

      return {
        id: updatedFile.id,
        projectId: updatedFile.projectId,
        fileName: updatedFile.fileName,
        fileType: updatedFile.fileType,
        fileSize: updatedFile.fileSize,
        mimeType: updatedFile.mimeType,
        storagePath: updatedFile.storagePath,
        publicUrl: updatedFile.publicUrl,
        uploadStatus: updatedFile.uploadStatus,
        processedAt: updatedFile.processedAt,
        metadata: updatedFile.metadata as Record<string, unknown> | null,
        createdAt: updatedFile.createdAt,
      };
    }),
});