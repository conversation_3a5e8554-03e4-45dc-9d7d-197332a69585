import { z } from "@hono/zod-openapi";

export const uploadBimFileSchema = z
  .object({
    projectId: z.string().uuid().openapi({
      description: "UUID of the project to upload BIM files for",
      example: "b7e6c8e2-1f2a-4c3b-9e2d-1a2b3c4d5e6f",
    }),
    fileName: z.string().min(1).openapi({
      description: "Name of the BIM file being uploaded",
      example: "building-model.ifc",
    }),
    fileType: z.enum(["ifc", "frag", "json"]).openapi({
      description: "Type of BIM file (IFC model, FRAG geometry, or JSON properties)",
      example: "ifc",
    }),
    fileSize: z.number().positive().openapi({
      description: "Size of the file in bytes",
      example: 15728640,
    }),
    mimeType: z.string().openapi({
      description: "MIME type of the uploaded file",
      example: "application/octet-stream",
    }),
  })
  .openapi("UploadBimFile");

export const bimFileUploadResponseSchema = z
  .object({
    uploadUrl: z.string().url().openapi({
      description: "Signed URL for uploading the file directly to storage",
      example: "https://vqbbkelqodoelnlfnsay.supabase.co/storage/v1/object/sign/bim-files/...",
    }),
    fileId: z.string().uuid().openapi({
      description: "Unique identifier for this BIM file",
      example: "a1b2c3d4-e5f6-7890-abcd-1234567890ef",
    }),
    storagePath: z.string().openapi({
      description: "Storage path where the file will be stored",
      example: "projects/b7e6c8e2-1f2a-4c3b-9e2d-1a2b3c4d5e6f/models/building-model.ifc",
    }),
    expiresAt: z.string().datetime().openapi({
      description: "When the upload URL expires (ISO 8601 format)",
      example: "2024-05-01T13:00:00.000Z",
    }),
  })
  .openapi("BimFileUploadResponse");

export const confirmBimFileUploadSchema = z
  .object({
    fileId: z.string().uuid().openapi({
      description: "File ID returned from the upload initiation",
      example: "a1b2c3d4-e5f6-7890-abcd-1234567890ef",
    }),
    projectId: z.string().uuid().openapi({
      description: "Project ID this file belongs to",
      example: "b7e6c8e2-1f2a-4c3b-9e2d-1a2b3c4d5e6f",
    }),
  })
  .openapi("ConfirmBimFileUpload");

export const getBimFilesSchema = z
  .object({
    projectId: z.string().uuid().openapi({
      description: "UUID of the project to get BIM files for",
      example: "b7e6c8e2-1f2a-4c3b-9e2d-1a2b3c4d5e6f",
    }),
    fileType: z.enum(["ifc", "frag", "json"]).optional().openapi({
      description: "Filter by specific file type",
      example: "ifc",
    }),
  })
  .openapi("GetBimFiles");

export const deleteBimFileSchema = z
  .object({
    fileId: z.string().uuid().openapi({
      description: "UUID of the BIM file to delete",
      example: "a1b2c3d4-e5f6-7890-abcd-1234567890ef",
    }),
  })
  .openapi("DeleteBimFile");

export const bimFileResponseSchema = z
  .object({
    id: z.string().uuid().openapi({
      description: "Unique identifier of the BIM file",
      example: "a1b2c3d4-e5f6-7890-abcd-1234567890ef",
    }),
    projectId: z.string().uuid().openapi({
      description: "Project this file belongs to",
      example: "b7e6c8e2-1f2a-4c3b-9e2d-1a2b3c4d5e6f",
    }),
    fileName: z.string().openapi({
      description: "Original name of the uploaded file",
      example: "building-model.ifc",
    }),
    fileType: z.enum(["ifc", "frag", "json"]).openapi({
      description: "Type of BIM file",
      example: "ifc",
    }),
    fileSize: z.number().openapi({
      description: "Size of the file in bytes",
      example: 15728640,
    }),
    mimeType: z.string().openapi({
      description: "MIME type of the file",
      example: "application/octet-stream",
    }),
    storagePath: z.string().openapi({
      description: "Storage path of the file",
      example: "projects/b7e6c8e2-1f2a-4c3b-9e2d-1a2b3c4d5e6f/models/building-model.ifc",
    }),
    publicUrl: z.string().url().nullable().openapi({
      description: "Public URL for accessing the file (null if file is private)",
      example: "https://vqbbkelqodoelnlfnsay.supabase.co/storage/v1/object/public/bim-files/...",
    }),
    uploadStatus: z.enum(["pending", "completed", "failed"]).openapi({
      description: "Status of the file upload",
      example: "completed",
    }),
    processedAt: z.string().datetime().nullable().openapi({
      description: "When the file was processed (for IFC files)",
      example: "2024-05-01T12:30:00.000Z",
    }),
    metadata: z.record(z.unknown()).nullable().openapi({
      description: "Additional metadata extracted from the file",
      example: { "elements": 1250, "version": "IFC4", "application": "Revit 2024" },
    }),
    createdAt: z.string().datetime().openapi({
      description: "When the file record was created",
      example: "2024-05-01T12:00:00.000Z",
    }),
  })
  .openapi("BimFileResponse");

export const bimFilesResponseSchema = z
  .object({
    data: z.array(bimFileResponseSchema).openapi({
      description: "Array of BIM files for the project",
    }),
    totalSize: z.number().openapi({
      description: "Total size of all BIM files in bytes",
      example: 47185920,
    }),
    summary: z.object({
      ifcFiles: z.number(),
      fragFiles: z.number(),
      jsonFiles: z.number(),
    }).openapi({
      description: "Summary count of different file types",
    }),
  })
  .openapi("BimFilesResponse");

export const processBimFileSchema = z
  .object({
    fileId: z.string().uuid().openapi({
      description: "UUID of the BIM file to process",
      example: "a1b2c3d4-e5f6-7890-abcd-1234567890ef",
    }),
    generateFragments: z.boolean().default(true).openapi({
      description: "Whether to generate FRAG geometry files from IFC",
      example: true,
    }),
    generateProperties: z.boolean().default(true).openapi({
      description: "Whether to extract JSON properties from IFC",
      example: true,
    }),
    optimizeForViewer: z.boolean().default(true).openapi({
      description: "Whether to optimize the output for web-based 3D viewing",
      example: true,
    }),
  })
  .openapi("ProcessBimFile");

export const bimFileDownloadUrlSchema = z
  .object({
    fileId: z.string().uuid().openapi({
      description: "UUID of the BIM file to download",
      example: "a1b2c3d4-e5f6-7890-abcd-1234567890ef",
    }),
    expireIn: z.number().min(60).max(3600).default(300).openapi({
      description: "How long the download URL should be valid (in seconds)",
      example: 300,
    }),
  })
  .openapi("BimFileDownloadUrl");

export const bimFileDownloadResponseSchema = z
  .object({
    downloadUrl: z.string().url().openapi({
      description: "Signed URL for downloading the file",
      example: "https://vqbbkelqodoelnlfnsay.supabase.co/storage/v1/object/sign/bim-files/...",
    }),
    fileName: z.string().openapi({
      description: "Original file name for download",
      example: "building-model.ifc",
    }),
    fileSize: z.number().openapi({
      description: "Size of the file in bytes",
      example: 15728640,
    }),
    expiresAt: z.string().datetime().openapi({
      description: "When the download URL expires",
      example: "2024-05-01T12:05:00.000Z",
    }),
  })
  .openapi("BimFileDownloadResponse");