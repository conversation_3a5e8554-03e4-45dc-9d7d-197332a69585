import { z } from "@hono/zod-openapi";

export const createBimTeamSchema = z
  .object({
    teamName: z.string().min(1).openapi({
      description: "Name of the BIM team",
      example: "Structural Foundation Team",
    }),
    teamRole: z.enum(["bim_manager", "structural", "mep", "architect", "contractor"]).openapi({
      description: "Role of the construction team",
      example: "structural",
    }),
    teamDescription: z.string().optional().openapi({
      description: "Detailed description of the team's responsibilities",
      example: "Responsible for all structural elements including foundations, columns, and beams",
    }),
    contactName: z.string().optional().openapi({
      description: "Primary contact person for the team",
      example: "<PERSON>",
    }),
    contactPhone: z.string().optional().openapi({
      description: "Contact phone number",
      example: "******-0123",
    }),
    projectId: z.string().uuid().openapi({
      description: "UUID of the project this team is assigned to",
      example: "b7e6c8e2-1f2a-4c3b-9e2d-1a2b3c4d5e6f",
    }),
    ifcGuids: z.array(z.string()).optional().openapi({
      description: "Array of IFC element GUIDs assigned to this team",
      example: ["2O2Fr$t4X7Zf8NOew3FLOH", "2O2Fr$t4X7Zf8NOew3FLOi"],
    }),
    camera: z.object({
      position: z.array(z.number()).length(3).openapi({
        description: "3D camera position coordinates [x, y, z]",
        example: [10.5, 15.2, 8.7],
      }),
      target: z.array(z.number()).length(3).openapi({
        description: "3D camera target coordinates [x, y, z]",
        example: [0, 0, 0],
      }),
      up: z.array(z.number()).length(3).optional().openapi({
        description: "3D camera up vector [x, y, z]",
        example: [0, 1, 0],
      }),
    }).optional().openapi({
      description: "3D camera position and orientation for this team's view",
    }),
  })
  .openapi("CreateBimTeam");

export const updateBimTeamElementsSchema = z
  .object({
    teamId: z.string().uuid().openapi({
      description: "UUID of the team to update",
      example: "f1e2d3c4-b5a6-7890-abcd-1234567890ef",
    }),
    ifcGuids: z.array(z.string()).openapi({
      description: "Updated array of IFC element GUIDs assigned to this team",
      example: ["2O2Fr$t4X7Zf8NOew3FLOH", "2O2Fr$t4X7Zf8NOew3FLOi", "2O2Fr$t4X7Zf8NOew3FLOj"],
    }),
    camera: z.object({
      position: z.array(z.number()).length(3),
      target: z.array(z.number()).length(3),
      up: z.array(z.number()).length(3).optional(),
    }).openapi({
      description: "Updated 3D camera position for this team's view",
    }),
  })
  .openapi("UpdateBimTeamElements");

export const getBimTeamsByProjectSchema = z.object({
  projectId: z.string().uuid().openapi({
    description: "UUID of the project to get teams for",
    example: "b7e6c8e2-1f2a-4c3b-9e2d-1a2b3c4d5e6f",
  }),
});

export const deleteBimTeamSchema = z.object({
  teamId: z.string().uuid().openapi({
    description: "UUID of the team to delete",
    example: "f1e2d3c4-b5a6-7890-abcd-1234567890ef",
  }),
});

export const bimTeamResponseSchema = z
  .object({
    id: z.string().uuid().openapi({
      description: "Unique identifier of the BIM team",
      example: "f1e2d3c4-b5a6-7890-abcd-1234567890ef",
    }),
    teamName: z.string().openapi({
      description: "Name of the BIM team",
      example: "Structural Foundation Team",
    }),
    teamRole: z.enum(["bim_manager", "structural", "mep", "architect", "contractor"]).openapi({
      description: "Role of the construction team",
      example: "structural",
    }),
    teamDescription: z.string().nullable().openapi({
      description: "Detailed description of the team's responsibilities",
      example: "Responsible for all structural elements including foundations, columns, and beams",
    }),
    contactName: z.string().nullable().openapi({
      description: "Primary contact person for the team",
      example: "John Smith",
    }),
    contactPhone: z.string().nullable().openapi({
      description: "Contact phone number",
      example: "******-0123",
    }),
    projectId: z.string().uuid().openapi({
      description: "UUID of the project this team is assigned to",
      example: "b7e6c8e2-1f2a-4c3b-9e2d-1a2b3c4d5e6f",
    }),
    ifcGuids: z.array(z.string()).nullable().openapi({
      description: "Array of IFC element GUIDs assigned to this team",
      example: ["2O2Fr$t4X7Zf8NOew3FLOH", "2O2Fr$t4X7Zf8NOew3FLOi"],
    }),
    camera: z.object({
      position: z.array(z.number()).length(3),
      target: z.array(z.number()).length(3),
      up: z.array(z.number()).length(3).optional(),
    }).nullable().openapi({
      description: "3D camera position and orientation for this team's view",
    }),
    numberOfElements: z.number().openapi({
      description: "Number of IFC elements assigned to this team",
      example: 24,
    }),
    createdAt: z.string().openapi({
      description: "Date and time when the team was created in ISO 8601 format",
      example: "2024-05-01T12:00:00.000Z",
    }),
  })
  .openapi("BimTeamResponse");

export const bimTeamsResponseSchema = z
  .object({
    data: z.array(bimTeamResponseSchema).openapi({
      description: "Array of BIM teams for the project",
    }),
  })
  .openapi("BimTeamsResponse");