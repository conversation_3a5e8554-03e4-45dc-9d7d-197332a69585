import { and, desc, eq, sql } from "drizzle-orm";
import { teamBimAssignments } from "@api/db/schema";
import type { Database } from "@api/db";

export type GetBimTeamsByProjectParams = {
  teamId: string;
  projectId: string;
};

export type CreateBimTeamParams = {
  teamId: string;
  teamName: string;
  teamRole: "bim_manager" | "structural" | "mep" | "architect" | "contractor";
  teamDescription?: string | null;
  contactName?: string | null;
  contactPhone?: string | null;
  projectId: string;
  ifcGuids?: string[] | null;
  camera?: {
    position: number[];
    target: number[];
    up?: number[];
  } | null;
};

export type UpdateBimTeamElementsParams = {
  teamId: string;
  ifcGuids: string[];
  camera: {
    position: number[];
    target: number[];
    up?: number[];
  };
};

export async function getBimTeamsByProject(
  db: Database,
  {
    teamId,
    projectId,
  }: GetBimTeamsByProjectParams
) {
  return db
    .select({
      id: teamBimAssignments.id,
      teamName: teamBimAssignments.teamName,
      teamRole: teamBimAssignments.teamRole,
      teamDescription: teamBimAssignments.teamDescription,
      contactName: teamBimAssignments.contactName,
      contactPhone: teamBimAssignments.contactPhone,
      projectId: teamBimAssignments.projectId,
      ifcGuids: teamBimAssignments.ifcGuids,
      camera: teamBimAssignments.camera,
      numberOfElements: sql<number>`
        CASE 
          WHEN ${teamBimAssignments.ifcGuids} IS NULL THEN 0
          ELSE jsonb_array_length(${teamBimAssignments.ifcGuids})
        END
      `,
      createdAt: teamBimAssignments.createdAt,
    })
    .from(teamBimAssignments)
    .where(
      and(
        eq(teamBimAssignments.teamId, teamId),
        eq(teamBimAssignments.projectId, projectId)
      )
    )
    .orderBy(desc(teamBimAssignments.createdAt));
}

export async function getAllBimTeamsForProject(db: Database, projectId: string) {
  return db
    .select({
      id: teamBimAssignments.id,
      teamName: teamBimAssignments.teamName,
      teamRole: teamBimAssignments.teamRole,
      teamDescription: teamBimAssignments.teamDescription,
      contactName: teamBimAssignments.contactName,
      contactPhone: teamBimAssignments.contactPhone,
      projectId: teamBimAssignments.projectId,
      ifcGuids: teamBimAssignments.ifcGuids,
      camera: teamBimAssignments.camera,
      numberOfElements: sql<number>`
        CASE 
          WHEN ${teamBimAssignments.ifcGuids} IS NULL THEN 0
          ELSE jsonb_array_length(${teamBimAssignments.ifcGuids})
        END
      `,
      createdAt: teamBimAssignments.createdAt,
    })
    .from(teamBimAssignments)
    .where(eq(teamBimAssignments.projectId, projectId))
    .orderBy(desc(teamBimAssignments.createdAt));
}

export async function createBimTeam(db: Database, params: CreateBimTeamParams) {
  const [result] = await db
    .insert(teamBimAssignments)
    .values({
      teamId: params.teamId,
      teamName: params.teamName,
      teamRole: params.teamRole,
      teamDescription: params.teamDescription,
      contactName: params.contactName,
      contactPhone: params.contactPhone,
      projectId: params.projectId,
      ifcGuids: params.ifcGuids ? JSON.stringify(params.ifcGuids) : null,
      camera: params.camera ? JSON.stringify(params.camera) : null,
    })
    .returning({
      id: teamBimAssignments.id,
      teamName: teamBimAssignments.teamName,
      teamRole: teamBimAssignments.teamRole,
      teamDescription: teamBimAssignments.teamDescription,
      contactName: teamBimAssignments.contactName,
      contactPhone: teamBimAssignments.contactPhone,
      projectId: teamBimAssignments.projectId,
      ifcGuids: teamBimAssignments.ifcGuids,
      camera: teamBimAssignments.camera,
      createdAt: teamBimAssignments.createdAt,
    });

  if (!result) {
    throw new Error("Failed to create BIM team assignment");
  }

  // Calculate numberOfElements for response
  const numberOfElements = result.ifcGuids
    ? JSON.parse(result.ifcGuids as string).length
    : 0;

  return {
    ...result,
    numberOfElements,
  };
}

export async function updateBimTeamElements(
  db: Database,
  {
    teamId,
    ifcGuids,
    camera,
  }: UpdateBimTeamElementsParams
) {
  const [result] = await db
    .update(teamBimAssignments)
    .set({
      ifcGuids: JSON.stringify(ifcGuids),
      camera: JSON.stringify(camera),
    })
    .where(eq(teamBimAssignments.id, teamId))
    .returning({
      id: teamBimAssignments.id,
      teamName: teamBimAssignments.teamName,
      teamRole: teamBimAssignments.teamRole,
      teamDescription: teamBimAssignments.teamDescription,
      contactName: teamBimAssignments.contactName,
      contactPhone: teamBimAssignments.contactPhone,
      projectId: teamBimAssignments.projectId,
      ifcGuids: teamBimAssignments.ifcGuids,
      camera: teamBimAssignments.camera,
      createdAt: teamBimAssignments.createdAt,
    });

  if (!result) {
    throw new Error("BIM team assignment not found");
  }

  // Calculate numberOfElements for response
  const numberOfElements = result.ifcGuids
    ? JSON.parse(result.ifcGuids as string).length
    : 0;

  return {
    ...result,
    numberOfElements,
  };
}

export async function deleteBimTeam(db: Database, teamId: string) {
  const [result] = await db
    .delete(teamBimAssignments)
    .where(eq(teamBimAssignments.id, teamId))
    .returning({
      id: teamBimAssignments.id,
    });

  if (!result) {
    throw new Error("BIM team assignment not found");
  }

  return result;
}

export async function getBimTeamById(db: Database, teamId: string) {
  const [result] = await db
    .select({
      id: teamBimAssignments.id,
      teamName: teamBimAssignments.teamName,
      teamRole: teamBimAssignments.teamRole,
      teamDescription: teamBimAssignments.teamDescription,
      contactName: teamBimAssignments.contactName,
      contactPhone: teamBimAssignments.contactPhone,
      projectId: teamBimAssignments.projectId,
      ifcGuids: teamBimAssignments.ifcGuids,
      camera: teamBimAssignments.camera,
      numberOfElements: sql<number>`
        CASE 
          WHEN ${teamBimAssignments.ifcGuids} IS NULL THEN 0
          ELSE jsonb_array_length(${teamBimAssignments.ifcGuids})
        END
      `,
      createdAt: teamBimAssignments.createdAt,
    })
    .from(teamBimAssignments)
    .where(eq(teamBimAssignments.id, teamId));

  if (!result) {
    throw new Error("BIM team assignment not found");
  }

  return result;
}