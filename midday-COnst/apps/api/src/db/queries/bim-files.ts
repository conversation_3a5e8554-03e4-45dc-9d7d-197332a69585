import { and, desc, eq, sql, sum } from "drizzle-orm";
import { bimFiles } from "@api/db/schema";
import type { Database } from "@api/db";

export type GetBimFilesParams = {
  teamId: string;
  projectId: string;
  fileType?: "ifc" | "frag" | "json";
};

export type CreateBimFileParams = {
  teamId: string;
  projectId: string;
  fileName: string;
  fileType: "ifc" | "frag" | "json";
  fileSize: number;
  mimeType: string;
  storagePath: string;
  publicUrl?: string | null;
};

export type UpdateBimFileParams = {
  id: string;
  uploadStatus?: "pending" | "completed" | "failed";
  publicUrl?: string | null;
  processedAt?: string | null;
  metadata?: Record<string, unknown> | null;
};

export async function getBimFilesByProject(
  db: Database,
  {
    teamId,
    projectId,
    fileType,
  }: GetBimFilesParams
) {
  const whereConditions = [
    eq(bimFiles.teamId, teamId),
    eq(bimFiles.projectId, projectId),
  ];

  if (fileType) {
    whereConditions.push(eq(bimFiles.fileType, fileType));
  }

  const files = await db
    .select({
      id: bimFiles.id,
      projectId: bimFiles.projectId,
      fileName: bimFiles.fileName,
      fileType: bimFiles.fileType,
      fileSize: bimFiles.fileSize,
      mimeType: bimFiles.mimeType,
      storagePath: bimFiles.storagePath,
      publicUrl: bimFiles.publicUrl,
      uploadStatus: bimFiles.uploadStatus,
      processedAt: bimFiles.processedAt,
      metadata: bimFiles.metadata,
      createdAt: bimFiles.createdAt,
    })
    .from(bimFiles)
    .where(and(...whereConditions))
    .orderBy(desc(bimFiles.createdAt));

  // Get summary statistics
  const summaryQuery = await db
    .select({
      totalSize: sum(bimFiles.fileSize),
      ifcCount: sql<number>`COUNT(CASE WHEN ${bimFiles.fileType} = 'ifc' THEN 1 END)`,
      fragCount: sql<number>`COUNT(CASE WHEN ${bimFiles.fileType} = 'frag' THEN 1 END)`,
      jsonCount: sql<number>`COUNT(CASE WHEN ${bimFiles.fileType} = 'json' THEN 1 END)`,
    })
    .from(bimFiles)
    .where(and(eq(bimFiles.teamId, teamId), eq(bimFiles.projectId, projectId)));

  const summary = summaryQuery[0];

  return {
    data: files,
    totalSize: Number(summary?.totalSize) || 0,
    summary: {
      ifcFiles: summary?.ifcCount || 0,
      fragFiles: summary?.fragCount || 0,
      jsonFiles: summary?.jsonCount || 0,
    },
  };
}

export async function getBimFileById(db: Database, id: string) {
  const [result] = await db
    .select({
      id: bimFiles.id,
      projectId: bimFiles.projectId,
      fileName: bimFiles.fileName,
      fileType: bimFiles.fileType,
      fileSize: bimFiles.fileSize,
      mimeType: bimFiles.mimeType,
      storagePath: bimFiles.storagePath,
      publicUrl: bimFiles.publicUrl,
      uploadStatus: bimFiles.uploadStatus,
      processedAt: bimFiles.processedAt,
      metadata: bimFiles.metadata,
      createdAt: bimFiles.createdAt,
    })
    .from(bimFiles)
    .where(eq(bimFiles.id, id));

  if (!result) {
    throw new Error("BIM file not found");
  }

  return result;
}

export async function createBimFile(db: Database, params: CreateBimFileParams) {
  const [result] = await db
    .insert(bimFiles)
    .values({
      teamId: params.teamId,
      projectId: params.projectId,
      fileName: params.fileName,
      fileType: params.fileType,
      fileSize: params.fileSize,
      mimeType: params.mimeType,
      storagePath: params.storagePath,
      publicUrl: params.publicUrl,
      uploadStatus: "pending",
    })
    .returning({
      id: bimFiles.id,
      projectId: bimFiles.projectId,
      fileName: bimFiles.fileName,
      fileType: bimFiles.fileType,
      fileSize: bimFiles.fileSize,
      mimeType: bimFiles.mimeType,
      storagePath: bimFiles.storagePath,
      publicUrl: bimFiles.publicUrl,
      uploadStatus: bimFiles.uploadStatus,
      processedAt: bimFiles.processedAt,
      metadata: bimFiles.metadata,
      createdAt: bimFiles.createdAt,
    });

  if (!result) {
    throw new Error("Failed to create BIM file record");
  }

  return result;
}

export async function updateBimFile(
  db: Database,
  {
    id,
    uploadStatus,
    publicUrl,
    processedAt,
    metadata,
  }: UpdateBimFileParams
) {
  const updateData: Partial<typeof bimFiles.$inferInsert> = {};

  if (uploadStatus !== undefined) updateData.uploadStatus = uploadStatus;
  if (publicUrl !== undefined) updateData.publicUrl = publicUrl;
  if (processedAt !== undefined) updateData.processedAt = processedAt;
  if (metadata !== undefined) updateData.metadata = metadata;

  // Always update the updatedAt timestamp
  updateData.updatedAt = new Date().toISOString();

  const [result] = await db
    .update(bimFiles)
    .set(updateData)
    .where(eq(bimFiles.id, id))
    .returning({
      id: bimFiles.id,
      projectId: bimFiles.projectId,
      fileName: bimFiles.fileName,
      fileType: bimFiles.fileType,
      fileSize: bimFiles.fileSize,
      mimeType: bimFiles.mimeType,
      storagePath: bimFiles.storagePath,
      publicUrl: bimFiles.publicUrl,
      uploadStatus: bimFiles.uploadStatus,
      processedAt: bimFiles.processedAt,
      metadata: bimFiles.metadata,
      createdAt: bimFiles.createdAt,
    });

  if (!result) {
    throw new Error("BIM file not found");
  }

  return result;
}

export async function deleteBimFile(db: Database, id: string) {
  const [result] = await db
    .delete(bimFiles)
    .where(eq(bimFiles.id, id))
    .returning({
      id: bimFiles.id,
      storagePath: bimFiles.storagePath,
      fileName: bimFiles.fileName,
    });

  if (!result) {
    throw new Error("BIM file not found");
  }

  return result;
}

export async function getBimFilesByType(
  db: Database,
  {
    teamId,
    projectId,
    fileType,
  }: {
    teamId: string;
    projectId: string;
    fileType: "ifc" | "frag" | "json";
  }
) {
  return db
    .select({
      id: bimFiles.id,
      fileName: bimFiles.fileName,
      storagePath: bimFiles.storagePath,
      publicUrl: bimFiles.publicUrl,
      uploadStatus: bimFiles.uploadStatus,
      fileSize: bimFiles.fileSize,
      createdAt: bimFiles.createdAt,
    })
    .from(bimFiles)
    .where(
      and(
        eq(bimFiles.teamId, teamId),
        eq(bimFiles.projectId, projectId),
        eq(bimFiles.fileType, fileType),
        eq(bimFiles.uploadStatus, "completed")
      )
    )
    .orderBy(desc(bimFiles.createdAt));
}

export async function getRelatedBimFiles(db: Database, ifcFileId: string) {
  // Get the IFC file details first
  const ifcFile = await getBimFileById(db, ifcFileId);
  
  if (ifcFile.fileType !== "ifc") {
    throw new Error("File is not an IFC file");
  }

  // Find related FRAG and JSON files for the same project
  const relatedFiles = await db
    .select({
      id: bimFiles.id,
      fileName: bimFiles.fileName,
      fileType: bimFiles.fileType,
      storagePath: bimFiles.storagePath,
      publicUrl: bimFiles.publicUrl,
      uploadStatus: bimFiles.uploadStatus,
      fileSize: bimFiles.fileSize,
      createdAt: bimFiles.createdAt,
    })
    .from(bimFiles)
    .where(
      and(
        eq(bimFiles.projectId, ifcFile.projectId),
        sql`${bimFiles.fileType} IN ('frag', 'json')`,
        eq(bimFiles.uploadStatus, "completed")
      )
    )
    .orderBy(desc(bimFiles.createdAt));

  return {
    ifcFile,
    relatedFiles,
  };
}

export async function updateBimFileMetadata(
  db: Database,
  id: string,
  metadata: Record<string, unknown>
) {
  return updateBimFile(db, {
    id,
    metadata,
    processedAt: new Date().toISOString(),
  });
}