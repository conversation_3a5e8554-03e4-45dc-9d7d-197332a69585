# **That Open Master Challenge** by <PERSON>
This is a web-based application designed to manage BIM projects by facilitating the interaction between IFC models and design teams. This project was developed as part of the BIM Software Development Master by That Open Company (formerly IFC.js).

### Project Overview
The application allows users to create, manage, and interact with BIM projects in real-time. It features:

- IFC Viewer Integration: View and interact with IFC models directly within the application, with predefined and custom tools for interacting with the model
- Real-time Collaboration: Teams and projects can be created, edited, and deleted with real-time updates, thanks to the integration with Firebase.

### Prerequisites
To run this project locally, ensure you have the following installed:
- Node.js
- npm

### Installation
1. Clone this repository to your local machine:

2. Navigate to the project directory:
```js
cd that-open-master-challenge
```

3. Install the required dependencies:
```js
npm install
```

### Running the Project
To run the application locally, use the following command:
```js
npm run dev
```

This will start the development server and allow you to view the application in your browser at http://localhost:3000.

### Explore the Code
Feel free to explore and modify the code. You can find the source files within the src directory, organized into various components and services.

### Contributions
Contributions are welcome! If you have suggestions for improvements or new features, please open an issue or submit a pull request.
