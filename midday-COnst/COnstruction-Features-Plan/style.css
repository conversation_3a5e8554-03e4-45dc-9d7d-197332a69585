@import url("https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400&family=Roboto:wght@300&display=swap");

:root {
  --primary: #3ecf31;
  --primary-100: #4a6e47;
  --primary-200: #415a66;
  --primary-300: #468f3f;
  --primary-400: #50ef5e;
  --background: #2d2d2d;
  --background-100: #3b3b3b;
  --background-200: #535353;
  --font-xs: 8px;
  --font-sm: 10px;
  --font-base: 12px;
  --font-lg: 14px;
  --font-xl: 16px;
  --font-2xl: 18px;
  --font-3xl: 20px;
  --bim-ui_bg-base: var(--background);
  --bim-ui_main-base: var(--primary-100);
}

* {
  font-family: "Inter", sans-serif;
  margin: 0;
  padding: 0;
}

#app {
  display: grid;
  grid-template-columns: 250px 1fr;
  min-height: 100vh;
  background-color: var(--background);
  color: #fff;
}

/* General Style */

h2 {
  font-size: var(--font-3xl);
}

h3 {
  font-size: var(--font-2xl);
}

h4 {
  font-size: var(--font-xl);
}

h5 {
  font-size: var(--font-lg);
}

button {
  padding: 8px 15px;
  cursor: pointer;
  height: 40px;
  font-size: 12px;
  background-color: var(--primary);
  color: white;
  border-radius: 100px;
  border: none;
  display: flex;
  align-items: center;
  column-gap: 5px;
}

button:hover {
  background-color: var(--primary-400);
}

.page {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* Sidebar Style */

#sidebar {
  background-color: var(--background-100);
  padding: 15px;
  display: flex;
  flex-direction: column;
  row-gap: 20px;
}

#company-logo {
  height: 120px;
}

.nav-buttons {
  display: flex;
  flex-direction: column;
  row-gap: 15px;
}

.nav-buttons li {
  list-style-type: none;
  padding: 15px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  border-radius: 10px;
  cursor: pointer;
  background-color: var(--primary);
  font-size: var(--font-lg);
}

.nav-buttons li:hover {
  background-color: var(--primary-100);
}

.nav-buttons a {
  text-decoration: none; /* Removes underline */
  color: inherit; /* Inherits color from parent */
}

.nav-buttons-secondary {
  background-color: transparent;
  outline: 2px solid #969696;
  width: 60px;
  height: 30px;
  text-align: center;
}

.nav-buttons-secondary:hover {
  background-color: var(--primary);
  outline: none;
}

/* Content Style */

header {
  padding: 0px 20px;
  margin-top: 20px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: space-between;
}

.card-content {
  display: flex;
  flex-direction: column;
  row-gap: 8px;
  align-items: start;
  font-size: var(--font-base);
}

.card-property {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  overflow: hidden;
}

/* Form Styles */

dialog {
  background-color: transparent;
  border: none;
  margin: auto;
}

dialog::backdrop {
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(3px);
}

form {
  width: 450px;
  border-radius: 10px;
  background-color: var(--background-100);
  color: white;
}

form h2 {
  display: flex;
  padding: 20px;
  border-bottom: 2px solid var(--background-200);
  justify-content: center;
  margin-bottom: 20px;
}

form .input-list {
  display: flex;
  flex-direction: column;
  padding: 0px 20px;
  row-gap: 10px;
  margin-bottom: 20px;
}

form .form-field-container {
  display: flex;
  padding: 0%;
  flex-direction: column;
}

label {
  font-size: var(--font-base);
  margin-bottom: 3px;
  font-weight: 500;
  color: rgba(150, 150, 150, 1);
  display: flex;
  align-items: center;
  column-gap: 5px;
}

input,
textarea,
select {
  color: white;
  background-color: var(--background);
  padding: 10px;
  border-radius: 8px;
  border: none;
  font-size: var(--font-base);
}

input:focus,
textarea:focus,
select:focus {
  outline: 2px solid var(--primary);
}

#error-message,
#info-message {
  display: flex;
  padding: 15px;
  border-radius: 10px;
  background-color: var(--background-100);
  color: white;
  align-items: center;
  flex-direction: column;
  row-gap: 10px;
}

.error-modal {
  display: flex;
  padding: 15px;
  border-radius: 10px;
  background-color: var(--background-100);
  color: white;
  align-items: center;
  flex-direction: column;
}

.project-form {
  max-height: calc(90vh);
  overflow-y: auto;
}

/* Project details page*/

#project-details {
  display: grid;
}

.dashboard-card {
  background-color: var(--background-200);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  height: 100%;
}

#project-details > .main-page-content {
  display: grid;
  padding: 0 10px;
  gap: 15px;
  grid-template-columns: 300px 1fr;
  height: 100%;
}

.project-column {
  display: grid;
  grid-template-rows: auto 1fr;
  gap: 15px;
  height: 100%;
  margin-bottom: 20px;
  max-height: calc(100vh - 100px);
}

.btn-secondary {
  background-color: transparent;
  outline: 2px solid #969696;
  width: 60px;
  height: 30px;
  text-align: center;
}

.btn-secondary:hover {
  background-color: var(--primary-300);
  outline: none;
}

#teams-header {
  padding-bottom: 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

#teams-list {
  display: flex;
  flex-direction: column;
  row-gap: 15px;
}

.team-card {
  padding: 10px;
  display: flex;
  flex-direction: row;
  /* row-gap: 20px; */
  background-color: var(--primary-300);
  border-radius: 10px;
  cursor: pointer;
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-base);
}

.team-card:hover {
  background-color: var(--primary-200);
}

/*HOMEPAGE */

.homepage {
  width: 100vw;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
}

.homepage-header {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
  width: 100%;
  position: relative;
}

#homepage-company-logo {
  height: 20vh;
  max-width: 100%;
  margin-right: 20px;
}

.projects-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
  padding: 15px;
  width: 100%;
  max-width: 1200px;
  min-height: 60vh;
  overflow-y: auto;
  background: var(--background-100);
  box-sizing: border-box;
  margin: 0 auto;
}

#homepage-new-project-btn {
  position: absolute;
  right: calc(50% - 200px);
  transform: translateX(100%);
}

.homepage-project-card {
  background-color: var(--background-200);
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  padding: 20px;
  max-width: 300px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  transition:
    transform 0.2s,
    box-shadow 0.2s;
  color: inherit;
  text-decoration: none;
}

.homepage-project-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

.homepage-card-content {
  display: flex;
  flex-direction: column;
  row-gap: 15px;
}

.homepage-card-property {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid var(--background-100);
}

.homepage-card-property:last-child {
  border-bottom: none;
}

.homepage-card-property h5 {
  margin: 0;
  font-size: var(--font-base);
  color: #969696;
}

#project-name {
  font-size: var(--font-2xl);
  margin: 0;
  color: var(--primary);
  text-decoration: none;
}

.progress-bar {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 10px;
}

.progress-label {
  font-size: var(--font-sm);
  color: #969696;
}

.progress-track {
  background-color: #404040;
  border-radius: 9999px;
  height: 10px;
  overflow: hidden;
}

.progress-fill {
  background-color: var(--primary-300);
  height: 100%;
  border-radius: 9999px;
  transition: width 0.3s ease;
}

.progress-percentage {
  font-size: var(--font-sm);
  color: var(--primary);
  text-align: right;
}

bim-option {
  padding: 4px;
}

bim-panel {
  bim-text-input,
  bim-dropdown,
  bim-button {
    margin-inline: 20px;
    margin-block: 10px;
  }
}

.router-link {
  text-decoration: none;
  color: inherit;
}

.router-link:hover {
  text-decoration: none;
}
